"""
CAN Network Simulator
Manages message transmission, arbitration, and network behavior
"""
import heapq
import time
import random
from collections import deque, defaultdict
from typing import Any, Callable, Dict, List,Optional
from dataclasses import dataclass, field
import numpy as np

from .can_message import CANMessage

@dataclass(order=True)
class NetworkEvent:
    """Represents a network event (message transmission)"""
    timestamp: float
    priority: int = field(compare=True)
    message: CANMessage = field(compare=False)
    event_type: str = field(default='transmit', compare=False)

class NetworkStats:
    """Tracks network statistics"""
    def __init__(self):
        self.messages_sent = 0
        self.messages_dropped = 0
        self.bus_errors = 0
        self.arbitration_lost = 0
        self.utilization_samples = deque(maxlen=1000)
        self.message_rates = defaultdict(lambda: deque(maxlen=100))
        self.error_frames = 0
        self.overload_frames = 0
    
    def record_message(self, msg_id: int, timestamp: float):
        """Record a successful message transmission"""
        self.messages_sent += 1
        self.message_rates[msg_id].append(timestamp)
    
    def calculate_bus_utilization(self, time_window: float = 1.0) -> float:
        """Calculate bus utilization percentage"""
        if not self.utilization_samples:
            return 0.0
        
        current_time = time.time()
        recent_samples = [s for s in self.utilization_samples 
                         if current_time - s < time_window]
        
        if not recent_samples:
            return 0.0
        
        # Assuming 500kbps baudrate and average 100 bits per message
        messages_per_second = len(recent_samples) / time_window
        theoretical_max = 5000  # 500kbps / 100 bits per message
        
        return min(100.0, (messages_per_second / theoretical_max) * 100)
    
    def get_message_rate(self, msg_id: int) -> float:
        """Get transmission rate for a specific message ID"""
        if msg_id not in self.message_rates or len(self.message_rates[msg_id]) < 2:
            return 0.0
        
        timestamps = list(self.message_rates[msg_id])
        time_span = timestamps[-1] - timestamps[0]
        
        if time_span > 0:
            return len(timestamps) / time_span
        return 0.0

class CANNetworkSimulator:
    """Simulates CAN bus network behavior"""
    def __init__(self, baudrate: int = 500000, max_nodes: int = 32):
        self.baudrate = baudrate
        self.max_nodes = max_nodes
        self.bit_time = 1.0 / baudrate
        
        # Network state
        self.bus_state = 'idle'  # 'idle', 'busy', 'error'
        self.current_time = 0.0
        self.event_queue: List[NetworkEvent] = []
        self.transmit_buffer: Dict[int, deque] = defaultdict(lambda: deque(maxlen=10))
        
        # Network parameters
        self.propagation_delay = 0.000001  # 1 microsecond
        self.arbitration_time = 0.000013  # 13 bit times for standard CAN
        self.message_overhead_bits = 47  # CAN protocol overhead
        
        # Error injection
        self.error_rate = 0.0001  # 0.01% bit error rate
        self.noise_level = 0.0
        
        # Callbacks
        self.message_callbacks: List[Callable[[CANMessage], None]] = []
        self.error_callbacks: List[Callable[[str, Any], None]] = []
        
        # Statistics
        self.stats = NetworkStats()
        
        # Message history
        self.message_history: deque = deque(maxlen=10000)
    
    def add_message_callback(self, callback: Callable[[CANMessage], None]):
        """Add a callback for received messages"""
        self.message_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str, Any], None]):
        """Add a callback for network errors"""
        self.error_callbacks.append(callback)
    
    def transmit(self, message: CANMessage) -> bool:
        """Attempt to transmit a message on the bus"""
        # Check bus state
        if self.bus_state == 'error':
            self.stats.messages_dropped += 1
            return False
        
        # Add to transmit buffer for the node
        node_id = message.id >> 6  # Simple node ID extraction
        self.transmit_buffer[node_id].append(message)
        
        # Schedule transmission event
        if self.bus_state == 'idle':
            self._schedule_transmission(message)
            return True
        else:
            # Bus busy, will retry later
            return False
    
    def _schedule_transmission(self, message: CANMessage):
        """Schedule a message transmission event"""
        # Calculate transmission time
        data_bits = message.dlc * 8
        total_bits = data_bits + self.message_overhead_bits
        transmission_time = total_bits * self.bit_time
        
        # Create event with priority based on CAN ID (lower ID = higher priority)
        event = NetworkEvent(
            timestamp=self.current_time + self.arbitration_time,
            priority=-message.id,  # Negative for correct heap ordering
            message=message,
            event_type='transmit'
        )
        
        heapq.heappush(self.event_queue, event)
        self.bus_state = 'busy'
    
    def _process_transmission(self, event: NetworkEvent):
        """Process a transmission event"""
        message = event.message
        
        # Simulate bit errors
        if random.random() < self.error_rate:
            self._handle_transmission_error(message)
            return
        
        # Simulate arbitration
        if self._check_arbitration_conflict(message):
            self.stats.arbitration_lost += 1
            # Reschedule with backoff
            backoff_time = random.uniform(0.001, 0.005)
            event.timestamp = self.current_time + backoff_time
            heapq.heappush(self.event_queue, event)
            return
        
        # Successful transmission
        message.timestamp = self.current_time
        self.message_history.append(message)
        self.stats.record_message(message.id, self.current_time)
        self.stats.utilization_samples.append(self.current_time)
        
        # Notify callbacks
        for callback in self.message_callbacks:
            callback(message)
        
        # Schedule bus idle
        data_bits = message.dlc * 8
        total_bits = data_bits + self.message_overhead_bits
        transmission_time = total_bits * self.bit_time
        
        idle_event = NetworkEvent(
            timestamp=self.current_time + transmission_time,
            priority=0,
            message=None,
            event_type='bus_idle'
        )
        heapq.heappush(self.event_queue, idle_event)
    
    def _check_arbitration_conflict(self, message: CANMessage) -> bool:
        """Check if there's an arbitration conflict"""
        # Simplified arbitration check
        if len(self.transmit_buffer) > 1:
            for node_buffer in self.transmit_buffer.values():
                if node_buffer and node_buffer[0].id < message.id:
                    return True
        return False
    
    def _handle_transmission_error(self, message: CANMessage):
        """Handle transmission error"""
        self.stats.bus_errors += 1
        self.stats.error_frames += 1
        
        # Notify error callbacks
        for callback in self.error_callbacks:
            callback('transmission_error', {
                'message_id': message.id,
                'timestamp': self.current_time
            })
        
        # Enter error recovery
        self.bus_state = 'error'
        recovery_event = NetworkEvent(
            timestamp=self.current_time + 0.001,  # 1ms recovery time
            priority=0,
            message=None,
            event_type='error_recovery'
        )
        heapq.heappush(self.event_queue, recovery_event)
    
    def inject_noise(self, noise_level: float):
        """Inject noise into the network"""
        self.noise_level = noise_level
        self.error_rate = min(0.1, self.error_rate + noise_level * 0.01)
    
    def inject_message(self, message: CANMessage):
        """Directly inject a message (bypassing normal transmission)"""
        message.timestamp = self.current_time
        self.message_history.append(message)
        
        # Notify callbacks
        for callback in self.message_callbacks:
            callback(message)
    
    def step(self, time_delta: float) -> List[CANMessage]:
        """Advance simulation by time_delta and return transmitted messages"""
        transmitted_messages = []
        target_time = self.current_time + time_delta
        
        while self.event_queue and self.event_queue[0].timestamp <= target_time:
            event = heapq.heappop(self.event_queue)
            self.current_time = event.timestamp
            
            if event.event_type == 'transmit':
                self._process_transmission(event)
                if event.message:
                    transmitted_messages.append(event.message)
            
            elif event.event_type == 'bus_idle':
                self.bus_state = 'idle'
                # Check for pending messages
                self._process_pending_messages()
            
            elif event.event_type == 'error_recovery':
                self.bus_state = 'idle'
                self._process_pending_messages()
        
        self.current_time = target_time
        return transmitted_messages
    
    def _process_pending_messages(self):
        """Process messages waiting in transmit buffers"""
        # Find highest priority message
        highest_priority_msg = None
        highest_priority_node = None
        
        for node_id, buffer in self.transmit_buffer.items():
            if buffer:
                msg = buffer[0]
                if highest_priority_msg is None or msg.id < highest_priority_msg.id:
                    highest_priority_msg = msg
                    highest_priority_node = node_id
        
        if highest_priority_msg:
            self.transmit_buffer[highest_priority_node].popleft()
            self._schedule_transmission(highest_priority_msg)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get network statistics"""
        return {
            'messages_sent': self.stats.messages_sent,
            'messages_dropped': self.stats.messages_dropped,
            'bus_errors': self.stats.bus_errors,
            'arbitration_lost': self.stats.arbitration_lost,
            'error_frames': self.stats.error_frames,
            'bus_utilization': self.stats.calculate_bus_utilization(),
            'current_state': self.bus_state
        }
    
    def get_message_history(self, count: int = 100) -> List[CANMessage]:
        """Get recent message history"""
        return list(self.message_history)[-count:]
    
    def reset(self):
        """Reset the network simulator"""
        self.bus_state = 'idle'
        self.current_time = 0.0
        self.event_queue.clear()
        self.transmit_buffer.clear()
        self.message_history.clear()
        self.stats = NetworkStats()

class NetworkAnomalyDetector:
    """Detects anomalies in CAN network traffic"""
    def __init__(self):
        self.baseline_rates = {}
        self.baseline_patterns = {}
        self.anomaly_threshold = 2.0  # Standard deviations
        self.learning_mode = True
        self.sample_count = 0
    
    def update(self, message: CANMessage) -> Optional[str]:
        """Update detector with new message and check for anomalies"""
        msg_id = message.id
        
        if self.learning_mode:
            # Learn baseline patterns
            if msg_id not in self.baseline_rates:
                self.baseline_rates[msg_id] = {
                    'timestamps': deque(maxlen=100),
                    'intervals': deque(maxlen=99)
                }
            
            timestamps = self.baseline_rates[msg_id]['timestamps']
            timestamps.append(message.timestamp)
            
            if len(timestamps) > 1:
                interval = timestamps[-1] - timestamps[-2]
                self.baseline_rates[msg_id]['intervals'].append(interval)
            
            self.sample_count += 1
            if self.sample_count > 1000:
                self.learning_mode = False
                self._calculate_baselines()
            
            return None
        
        else:
            # Detect anomalies
            return self._check_anomaly(message)
    
    def _calculate_baselines(self):
        """Calculate baseline statistics"""
        for msg_id, data in self.baseline_rates.items():
            if data['intervals']:
                intervals = np.array(data['intervals'])
                self.baseline_patterns[msg_id] = {
                    'mean_interval': np.mean(intervals),
                    'std_interval': np.std(intervals),
                    'min_interval': np.min(intervals),
                    'max_interval': np.max(intervals)
                }
    
    def _check_anomaly(self, message: CANMessage) -> Optional[str]:
        """Check if message represents an anomaly"""
        msg_id = message.id
        
        # Unknown message ID
        if msg_id not in self.baseline_patterns:
            return f"unknown_message_id_{msg_id:03X}"
        
        # Check timing anomaly
        if msg_id in self.baseline_rates:
            timestamps = self.baseline_rates[msg_id]['timestamps']
            if timestamps:
                last_timestamp = timestamps[-1]
                interval = message.timestamp - last_timestamp
                
                baseline = self.baseline_patterns[msg_id]
                mean_interval = baseline['mean_interval']
                std_interval = baseline['std_interval']
                
                if abs(interval - mean_interval) > self.anomaly_threshold * std_interval:
                    return f"timing_anomaly_{msg_id:03X}"
        
        return None