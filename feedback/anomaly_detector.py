"""
Anomaly Detection Module
Detects various types of anomalies in CAN bus traffic
"""
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, deque
from dataclasses import dataclass
import time
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest

from simulation.can_message import CANMessage
from utils.data_structures import CircularBuffer, MessageTracker

@dataclass
class Anomaly:
    """Represents a detected anomaly"""
    type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    message_id: int
    timestamp: float
    description: str
    details: Dict[str, Any]
    confidence: float = 1.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'type': self.type,
            'severity': self.severity,
            'message_id': self.message_id,
            'timestamp': self.timestamp,
            'description': self.description,
            'details': self.details,
            'confidence': self.confidence
        }


class AnomalyDetector:
    """Main anomaly detection system"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize anomaly detector

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}

        # Sub-detectors
        self.timing_detector = TimingAnomalyDetector()
        self.value_detector = ValueRangeAnomalyDetector()
        self.correlation_detector = CorrelationAnomalyDetector()
        self.sequence_detector = SequenceAnomalyDetector()
        self.error_detector = ErrorFlagDetector()

        # Anomaly history
        self.anomaly_history = deque(maxlen=1000)
        self.anomaly_counts = defaultdict(int)

        # Learning mode
        self.learning_mode = True
        self.messages_processed = 0
        self.learning_threshold = self.config.get('learning_threshold', 1000)

    def process_message(self, message: CANMessage,
                       context: Optional[Dict[str, Any]] = None) -> List[Anomaly]:
        """
        Process a CAN message and detect anomalies

        Args:
            message: CAN message to analyze
            context: Additional context (e.g., vehicle state)

        Returns:
            List of detected anomalies
        """
        anomalies = []

        # Update sub-detectors
        self.timing_detector.update(message)
        self.value_detector.update(message)
        self.correlation_detector.update(message)
        self.sequence_detector.update(message)
        self.error_detector.update(message, context)

        # Check for anomalies if not in learning mode
        if not self.learning_mode:
            # Timing anomalies
            timing_anomaly = self.timing_detector.detect(message)
            if timing_anomaly:
                anomalies.append(timing_anomaly)

            # Value anomalies
            value_anomalies = self.value_detector.detect(message)
            anomalies.extend(value_anomalies)

            # Correlation anomalies
            correlation_anomaly = self.correlation_detector.detect(message)
            if correlation_anomaly:
                anomalies.append(correlation_anomaly)

            # Sequence anomalies
            sequence_anomaly = self.sequence_detector.detect(message)
            if sequence_anomaly:
                anomalies.append(sequence_anomaly)

            # Error flag anomalies
            error_anomaly = self.error_detector.detect(message, context)
            if error_anomaly:
                anomalies.append(error_anomaly)

        # Update counters
        self.messages_processed += 1
        if self.learning_mode and self.messages_processed >= self.learning_threshold:
            self.end_learning_mode()

        # Store anomalies
        for anomaly in anomalies:
            self.anomaly_history.append(anomaly)
            self.anomaly_counts[anomaly.type] += 1

        return anomalies

    def end_learning_mode(self):
        """End learning mode and prepare detectors"""
        self.learning_mode = False

        # Prepare sub-detectors
        self.timing_detector.prepare()
        self.value_detector.prepare()
        self.correlation_detector.prepare()
        self.sequence_detector.prepare()

        print(f"Anomaly detector learning complete after {self.messages_processed} messages")

    def get_statistics(self) -> Dict[str, Any]:
        """Get detection statistics"""
        return {
            'messages_processed': self.messages_processed,
            'learning_mode': self.learning_mode,
            'total_anomalies': len(self.anomaly_history),
            'anomaly_types': dict(self.anomaly_counts),
            'detection_rate': len(self.anomaly_history) / max(1, self.messages_processed)
        }


class TimingAnomalyDetector:
    """Detects timing-based anomalies"""

    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.message_times = defaultdict(lambda: deque(maxlen=window_size))
        self.inter_arrival_times = defaultdict(list)
        self.baseline_stats = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        msg_id = message.id
        timestamp = message.timestamp

        # Calculate inter-arrival time
        if self.message_times[msg_id]:
            iat = timestamp - self.message_times[msg_id][-1]
            self.inter_arrival_times[msg_id].append(iat)

        self.message_times[msg_id].append(timestamp)

    def prepare(self):
        """Prepare detector after learning phase"""
        # Calculate baseline statistics
        for msg_id, iats in self.inter_arrival_times.items():
            if len(iats) > 10:
                self.baseline_stats[msg_id] = {
                    'mean': np.mean(iats),
                    'std': np.std(iats),
                    'min': np.min(iats),
                    'max': np.max(iats),
                    'percentile_5': np.percentile(iats, 5),
                    'percentile_95': np.percentile(iats, 95)
                }

    def detect(self, message: CANMessage) -> Optional[Anomaly]:
        """Detect timing anomaly"""
        msg_id = message.id

        if msg_id not in self.baseline_stats or len(self.message_times[msg_id]) < 2:
            return None

        # Get latest IAT
        current_iat = message.timestamp - self.message_times[msg_id][-2]
        baseline = self.baseline_stats[msg_id]

        # Check for anomaly
        if baseline['std'] > 0:
            z_score = abs((current_iat - baseline['mean']) / baseline['std'])

            if z_score > 3:
                severity = 'high' if z_score > 5 else 'medium'
                return Anomaly(
                    type='timing_anomaly',
                    severity=severity,
                    message_id=msg_id,
                    timestamp=message.timestamp,
                    description=f"Abnormal inter-arrival time: {current_iat:.3f}s (z-score: {z_score:.2f})",
                    details={
                        'iat': current_iat,
                        'expected_mean': baseline['mean'],
                        'z_score': z_score
                    },
                    confidence=min(1.0, z_score / 10)
                )

        # Check absolute bounds
        if current_iat < baseline['percentile_5'] * 0.5 or current_iat > baseline['percentile_95'] * 2:
            return Anomaly(
                type='timing_anomaly',
                severity='low',
                message_id=msg_id,
                timestamp=message.timestamp,
                description=f"IAT outside expected bounds: {current_iat:.3f}s",
                details={
                    'iat': current_iat,
                    'expected_range': (baseline['percentile_5'], baseline['percentile_95'])
                }
            )

        return None


class ValueRangeAnomalyDetector:
    """Detects signal value anomalies"""

    def __init__(self):
        self.signal_values = defaultdict(lambda: defaultdict(list))
        self.signal_bounds = defaultdict(dict)
        self.isolation_forests = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                self.signal_values[message.id][signal_name].append(value)

    def prepare(self):
        """Prepare detector after learning phase"""
        # Calculate bounds and train isolation forests
        for msg_id, signals in self.signal_values.items():
            for signal_name, values in signals.items():
                if len(values) > 50:
                    key = f"{msg_id:03X}_{signal_name}"

                    # Calculate statistical bounds
                    self.signal_bounds[key] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values),
                        'percentile_1': np.percentile(values, 1),
                        'percentile_99': np.percentile(values, 99)
                    }

                    # Train isolation forest for anomaly detection
                    if len(values) > 100:
                        X = np.array(values).reshape(-1, 1)
                        self.isolation_forests[key] = IsolationForest(
                            contamination=0.05,
                            random_state=42
                        ).fit(X)

    def detect(self, message: CANMessage) -> List[Anomaly]:
        """Detect value anomalies"""
        anomalies = []

        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is None:
                continue

            key = f"{message.id:03X}_{signal_name}"

            # Check bounds
            if key in self.signal_bounds:
                bounds = self.signal_bounds[key]

                # Check if value is outside expected range
                if value < bounds['percentile_1'] or value > bounds['percentile_99']:
                    severity = 'high' if (value < bounds['min'] * 0.5 or
                                        value > bounds['max'] * 1.5) else 'medium'

                    anomalies.append(Anomaly(
                        type='value_range_violation',
                        severity=severity,
                        message_id=message.id,
                        timestamp=message.timestamp,
                        description=f"Signal {signal_name} value {value} outside expected range",
                        details={
                            'signal_name': signal_name,
                            'value': value,
                            'expected_range': (bounds['percentile_1'], bounds['percentile_99'])
                        }
                    ))

                # Check with isolation forest
                if key in self.isolation_forests:
                    prediction = self.isolation_forests[key].predict([[value]])
                    if prediction[0] == -1:  # Anomaly
                        anomalies.append(Anomaly(
                            type='value_anomaly_ml',
                            severity='medium',
                            message_id=message.id,
                            timestamp=message.timestamp,
                            description=f"ML model detected anomalous value for {signal_name}",
                            details={
                                'signal_name': signal_name,
                                'value': value,
                                'detection_method': 'isolation_forest'
                            }
                        ))

        return anomalies


class CorrelationAnomalyDetector:
    """Detects correlation disruptions between signals"""

    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.signal_buffers = defaultdict(lambda: CircularBuffer(window_size))
        self.correlation_baselines = {}

    def update(self, message: CANMessage):
        """Update with new message"""
        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                self.signal_buffers[key].append(value)

    def prepare(self):
        """Prepare detector after learning phase"""
        # Calculate baseline correlations
        signal_keys = list(self.signal_buffers.keys())

        for i in range(len(signal_keys)):
            for j in range(i + 1, len(signal_keys)):
                key1, key2 = signal_keys[i], signal_keys[j]

                if len(self.signal_buffers[key1]) >= 50 and len(self.signal_buffers[key2]) >= 50:
                    values1 = self.signal_buffers[key1].to_list()
                    values2 = self.signal_buffers[key2].to_list()

                    # Align lengths
                    min_len = min(len(values1), len(values2))
                    values1 = values1[-min_len:]
                    values2 = values2[-min_len:]

                    # Calculate correlation
                    if np.std(values1) > 0 and np.std(values2) > 0:
                        correlation = np.corrcoef(values1, values2)[0, 1]

                        if abs(correlation) > 0.5:  # Significant correlation
                            self.correlation_baselines[(key1, key2)] = {
                                'baseline_corr': correlation,
                                'threshold': 0.3  # Allowed deviation
                            }

    def detect(self, message: CANMessage) -> Optional[Anomaly]:
        """Detect correlation anomaly"""
        # Check correlations involving this message's signals
        for signal_name in message.signals:
            key = f"{message.id:03X}_{signal_name}"

            for (key1, key2), baseline in self.correlation_baselines.items():
                if key == key1 or key == key2:
                    # Calculate current correlation
                    if (len(self.signal_buffers[key1]) >= 20 and
                        len(self.signal_buffers[key2]) >= 20):

                        values1 = self.signal_buffers[key1].to_list()[-20:]
                        values2 = self.signal_buffers[key2].to_list()[-20:]

                        if len(values1) == len(values2) and np.std(values1) > 0 and np.std(values2) > 0:
                            current_corr = np.corrcoef(values1, values2)[0, 1]

                            # Check deviation
                            deviation = abs(current_corr - baseline['baseline_corr'])
                            if deviation > baseline['threshold']:
                                return Anomaly(
                                    type='correlation_disruption',
                                    severity='medium',
                                    message_id=message.id,
                                    timestamp=message.timestamp,
                                    description=f"Correlation disruption between {key1} and {key2}",
                                    details={
                                        'signals': (key1, key2),
                                        'baseline_correlation': baseline['baseline_corr'],
                                        'current_correlation': current_corr,
                                        'deviation': deviation
                                    }
                                )

        return None


class SequenceAnomalyDetector:
    """Detects sequence violations in message patterns"""

    def __init__(self, sequence_length: int = 5):
        self.sequence_length = sequence_length
        self.message_sequence = deque(maxlen=100)
        self.valid_sequences = set()
        self.sequence_counts = defaultdict(int)

    def update(self, message: CANMessage):
        """Update with new message"""
        self.message_sequence.append(message.id)

        if len(self.message_sequence) >= self.sequence_length:
            sequence = tuple(list(self.message_sequence)[-self.sequence_length:])
            self.sequence_counts[sequence] += 1

    def prepare(self):
        """Prepare detector after learning phase"""
        # Identify valid sequences (seen more than once)
        for sequence, count in self.sequence_counts.items():
            if count > 1:
                self.valid_sequences.add(sequence)

    def detect(self, message: CANMessage) -> Optional[Anomaly]:
        """Detect sequence anomaly"""
        if len(self.message_sequence) < self.sequence_length:
            return None

        current_sequence = tuple(list(self.message_sequence)[-self.sequence_length:])

        # Check if sequence is valid
        if current_sequence not in self.valid_sequences and len(self.valid_sequences) > 10:
            # Find closest valid sequence
            min_distance = float('inf')
            closest_sequence = None

            for valid_seq in self.valid_sequences:
                distance = sum(1 for a, b in zip(current_sequence, valid_seq) if a != b)
                if distance < min_distance:
                    min_distance = distance
                    closest_sequence = valid_seq

            if min_distance > 0:
                severity = 'high' if min_distance > 3 else 'medium'
                return Anomaly(
                    type='sequence_violation',
                    severity=severity,
                    message_id=message.id,
                    timestamp=message.timestamp,
                    description=f"Invalid message sequence detected",
                    details={
                        'sequence': current_sequence,
                        'closest_valid': closest_sequence,
                        'distance': min_distance
                    }
                )

        return None


class ErrorFlagDetector:
    """Detects error conditions and flags"""

    def __init__(self):
        self.error_signals = {
            'ERROR_FLAG', 'FAULT_CODE', 'ERROR_STATE',
            'MALFUNCTION', 'WARNING', 'DIAGNOSTIC'
        }
        self.error_history = defaultdict(list)

    def update(self, message: CANMessage, context: Optional[Dict[str, Any]] = None):
        """Update with new message"""
        # Check for error-related signals
        for signal_name in message.signals:
            if any(error_term in signal_name.upper() for error_term in self.error_signals):
                value = message.get_signal_value(signal_name)
                if value is not None and value > 0:
                    self.error_history[message.id].append({
                        'signal': signal_name,
                        'value': value,
                        'timestamp': message.timestamp
                    })

    def prepare(self):
        """Prepare detector after learning phase"""
        # No specific preparation needed
        pass

    def detect(self, message: CANMessage,
              context: Optional[Dict[str, Any]] = None) -> Optional[Anomaly]:
        """Detect error flag anomaly"""
        # Check for error signals
        for signal_name in message.signals:
            if any(error_term in signal_name.upper() for error_term in self.error_signals):
                value = message.get_signal_value(signal_name)

                if value is not None and value > 0:
                    # Determine severity based on context
                    severity = 'high'
                    if context:
                        if context.get('ecu_state') == 'ERROR':
                            severity = 'critical'
                        elif context.get('safety_critical', False):
                            severity = 'critical'

                    return Anomaly(
                        type='error_flag_trigger',
                        severity=severity,
                        message_id=message.id,
                        timestamp=message.timestamp,
                        description=f"Error flag triggered: {signal_name} = {value}",
                        details={
                            'signal_name': signal_name,
                            'error_value': value,
                            'context': context or {}
                        }
                    )

        return None