"""
Multi-objective Reward Calculator
Implements the reward function for guiding the DRL agent
"""
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, deque
from dataclasses import dataclass
import time

from simulation.can_message import CANMessage

@dataclass
class AnomalyScore:
    """Score for different types of anomalies"""
    timing_anomaly: float = 0.0
    value_range_violation: float = 0.0
    correlation_disruption: float = 0.0
    sequence_violation: float = 0.0
    error_flag_trigger: float = 0.0

    def total_score(self, weights: Dict[str, float]) -> float:
        """Calculate weighted total score"""
        return (
            weights.get('timing', 0.2) * self.timing_anomaly +
            weights.get('value', 0.3) * self.value_range_violation +
            weights.get('correlation', 0.2) * self.correlation_disruption +
            weights.get('sequence', 0.2) * self.sequence_violation +
            weights.get('error', 0.1) * self.error_flag_trigger
        )

class RewardCalculator:
    """Calculates multi-objective rewards for fuzzing actions"""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize reward calculator

        Args:
            config: Configuration containing:
                - vulnerability_weight: Weight for vulnerability discovery
                - exploration_weight: Weight for exploration
                - coverage_weight: Weight for coverage
                - novelty_weight: Weight for novelty
                - anomaly_weights: Weights for different anomaly types
                - safety_violation_penalty: Penalty for safety violations
                - repeated_action_penalty: Penalty for repeated actions
        """
        self.config = config

        # Reward weights
        self.vulnerability_weight = config.get('vulnerability_weight', 0.4)
        self.exploration_weight = config.get('exploration_weight', 0.2)
        self.coverage_weight = config.get('coverage_weight', 0.2)
        self.novelty_weight = config.get('novelty_weight', 0.2)

        # Anomaly weights
        self.anomaly_weights = config.get('anomaly_weights', {
            'timing': 0.2,
            'value': 0.3,
            'correlation': 0.2,
            'sequence': 0.2,
            'error': 0.1
        })

        # Penalties
        self.safety_violation_penalty = config.get('safety_violation_penalty', -10.0)
        self.repeated_action_penalty = config.get('repeated_action_penalty', -0.5)

        # State tracking
        self.message_history = deque(maxlen=1000)
        self.action_history = deque(maxlen=100)
        self.explored_states = set()
        self.message_coverage = defaultdict(set)
        self.anomaly_history = deque(maxlen=100)

        # Baseline statistics
        self.baseline_stats = {}
        self.update_baseline_interval = 1000
        self.messages_since_baseline = 0

    def calculate_reward(self,
                        action: Dict[str, Any],
                        pre_state: Dict[str, Any],
                        post_state: Dict[str, Any],
                        messages: List[CANMessage],
                        anomalies: List[Dict[str, Any]]) -> Tuple[float, Dict[str, float]]:
        """
        Calculate reward for an action

        Args:
            action: Action taken
            pre_state: State before action
            post_state: State after action
            messages: Messages transmitted after action
            anomalies: Detected anomalies

        Returns:
            total_reward: Total reward value
            reward_components: Breakdown of reward components
        """
        # Update history
        self.message_history.extend(messages)
        self.action_history.append(action)
        self.anomaly_history.extend(anomalies)

        # Calculate reward components
        vulnerability_reward = self._calculate_vulnerability_reward(anomalies)
        exploration_reward = self._calculate_exploration_reward(action, post_state)
        coverage_reward = self._calculate_coverage_reward(messages)
        novelty_reward = self._calculate_novelty_reward(messages, anomalies)

        # Apply penalties
        safety_penalty = self._calculate_safety_penalty(action, post_state)
        repetition_penalty = self._calculate_repetition_penalty(action)

        # Combine rewards
        total_reward = (
            self.vulnerability_weight * vulnerability_reward +
            self.exploration_weight * exploration_reward +
            self.coverage_weight * coverage_reward +
            self.novelty_weight * novelty_reward +
            safety_penalty +
            repetition_penalty
        )

        # Track components for analysis
        reward_components = {
            'vulnerability': vulnerability_reward,
            'exploration': exploration_reward,
            'coverage': coverage_reward,
            'novelty': novelty_reward,
            'safety_penalty': safety_penalty,
            'repetition_penalty': repetition_penalty,
            'total': total_reward
        }

        return total_reward, reward_components

    def _calculate_vulnerability_reward(self, anomalies: List[Dict[str, Any]]) -> float:
        """Calculate reward for vulnerability discovery"""
        if not anomalies:
            return 0.0

        # Score each anomaly
        total_score = 0.0
        for anomaly in anomalies:
            anomaly_type = anomaly.get('type', '')

            # Create anomaly score
            score = AnomalyScore()

            if 'timing' in anomaly_type:
                score.timing_anomaly = self._score_timing_anomaly(anomaly)
            elif 'value' in anomaly_type or 'range' in anomaly_type:
                score.value_range_violation = self._score_value_anomaly(anomaly)
            elif 'correlation' in anomaly_type:
                score.correlation_disruption = self._score_correlation_anomaly(anomaly)
            elif 'sequence' in anomaly_type:
                score.sequence_violation = self._score_sequence_anomaly(anomaly)
            elif 'error' in anomaly_type:
                score.error_flag_trigger = self._score_error_anomaly(anomaly)

            total_score += score.total_score(self.anomaly_weights)

        # Bonus for multiple anomalies
        if len(anomalies) > 1:
            total_score *= (1 + 0.1 * (len(anomalies) - 1))

        return min(10.0, total_score)  # Cap at 10

    def _score_timing_anomaly(self, anomaly: Dict[str, Any]) -> float:
        """Score timing-based anomalies"""
        # Base score
        score = 2.0

        # Increase for significant deviations
        if 'deviation' in anomaly:
            deviation = anomaly['deviation']
            if deviation > 0.1:  # More than 100ms
                score += 1.0
            if deviation > 0.5:  # More than 500ms
                score += 2.0

        return score

    def _score_value_anomaly(self, anomaly: Dict[str, Any]) -> float:
        """Score value range violations"""
        score = 3.0  # Higher base score for value violations

        # Check if it's a critical signal
        if 'signal_name' in anomaly:
            critical_signals = ['BRAKE_PRESSURE', 'STEERING_ANGLE', 'ENGINE_RPM']
            if any(sig in anomaly['signal_name'] for sig in critical_signals):
                score *= 2.0

        return score

    def _score_correlation_anomaly(self, anomaly: Dict[str, Any]) -> float:
        """Score correlation disruptions"""
        return 2.5  # Medium score for correlation issues

    def _score_sequence_anomaly(self, anomaly: Dict[str, Any]) -> float:
        """Score sequence violations"""
        return 2.0  # Base score for sequence issues

    def _score_error_anomaly(self, anomaly: Dict[str, Any]) -> float:
        """Score error flag triggers"""
        score = 4.0  # High score for error conditions

        # Check if it's a critical ECU
        if 'ecu_id' in anomaly:
            critical_ecus = [1, 2, 3]  # Engine, Brake, Steering
            if anomaly['ecu_id'] in critical_ecus:
                score *= 1.5

        return score

    def _calculate_exploration_reward(self, action: Dict[str, Any], state: Dict[str, Any]) -> float:
        """Calculate reward for exploration"""
        # Create state hash
        state_hash = self._hash_state(state)

        # Reward for visiting new states
        if state_hash not in self.explored_states:
            self.explored_states.add(state_hash)
            return 2.0

        # Small reward for revisiting with different action
        action_hash = self._hash_action(action)
        state_action_pair = (state_hash, action_hash)

        if state_action_pair not in self.explored_states:
            self.explored_states.add(state_action_pair)
            return 0.5

        return 0.0

    def _calculate_coverage_reward(self, messages: List[CANMessage]) -> float:
        """Calculate reward for message coverage"""
        new_coverage = 0

        for msg in messages:
            msg_id = msg.id

            # Track signal values seen
            for signal_name in msg.signals:
                value = msg.get_signal_value(signal_name)
                if value is not None:
                    value_bucket = int(value / 10) * 10  # Bucket values
                    key = (signal_name, value_bucket)

                    if key not in self.message_coverage[msg_id]:
                        self.message_coverage[msg_id].add(key)
                        new_coverage += 1

        # Reward based on new coverage
        return min(3.0, new_coverage * 0.5)

    def _calculate_novelty_reward(self, messages: List[CANMessage], anomalies: List[Dict[str, Any]]) -> float:
        """Calculate reward for novel behaviors"""
        novelty_score = 0.0

        # Check for new message patterns
        if len(messages) >= 3:
            # Simple pattern: sequence of message IDs
            pattern = tuple(msg.id for msg in messages[-3:])
            pattern_hash = hash(pattern)

            if pattern_hash not in self.explored_states:
                self.explored_states.add(pattern_hash)
                novelty_score += 1.0

        # Bonus for new types of anomalies
        if anomalies:
            anomaly_types = set(a.get('type', '') for a in anomalies)
            recent_types = set(a.get('type', '') for a in self.anomaly_history)

            new_types = anomaly_types - recent_types
            novelty_score += len(new_types) * 0.5

        return min(2.0, novelty_score)

    def _calculate_safety_penalty(self, action: Dict[str, Any], state: Dict[str, Any]) -> float:
        """Calculate penalty for safety violations"""
        penalty = 0.0

        # Check for safety violations in state
        if 'safety_violations' in state and state['safety_violations'] > 0:
            penalty += self.safety_violation_penalty * state['safety_violations']

        # Check for dangerous parameter values
        if action['type'] == 'value_manipulation':
            values = action.get('parameters', {}).get('values', {})

            # Check brake pressure
            if 'BRAKE_PRESSURE' in values and values['BRAKE_PRESSURE'] > 150:
                penalty += self.safety_violation_penalty * 0.5

            # Check steering angle
            if 'STEERING_ANGLE' in values and abs(values['STEERING_ANGLE']) > 500:
                penalty += self.safety_violation_penalty * 0.5

        return penalty

    def _calculate_repetition_penalty(self, action: Dict[str, Any]) -> float:
        """Calculate penalty for repeated actions"""
        # Count recent similar actions
        action_hash = self._hash_action(action)
        recent_hashes = [self._hash_action(a) for a in list(self.action_history)[-10:]]

        repetitions = recent_hashes.count(action_hash)

        if repetitions > 1:
            return self.repeated_action_penalty * (repetitions - 1)

        return 0.0

    def _hash_state(self, state: Dict[str, Any]) -> int:
        """Create hash of state for tracking"""
        # Simplified - in practice would be more sophisticated
        vehicle_state = state.get('vehicle_state', np.zeros(7))

        # Discretize continuous values
        discretized = tuple(int(v / 10) for v in vehicle_state[:5])

        return hash(discretized)

    def _hash_action(self, action: Dict[str, Any]) -> int:
        """Create hash of action for tracking"""
        # Hash based on type and target
        return hash((action['type'], action.get('target_id', 0)))

    def update_baseline(self, messages: List[CANMessage]):
        """Update baseline statistics for anomaly detection"""
        self.messages_since_baseline += len(messages)

        if self.messages_since_baseline >= self.update_baseline_interval:
            # Update baseline stats
            self._calculate_baseline_stats()
            self.messages_since_baseline = 0

    def _calculate_baseline_stats(self):
        """Calculate baseline statistics from message history"""
        # Group messages by ID
        messages_by_id = defaultdict(list)
        for msg in self.message_history:
            messages_by_id[msg.id].append(msg)

        # Calculate stats for each message type
        for msg_id, msgs in messages_by_id.items():
            if len(msgs) < 10:
                continue

            # Timing statistics
            timestamps = [msg.timestamp for msg in msgs]
            if len(timestamps) > 1:
                intervals = np.diff(timestamps)
                self.baseline_stats[f"{msg_id}_interval_mean"] = np.mean(intervals)
                self.baseline_stats[f"{msg_id}_interval_std"] = np.std(intervals)

            # Signal statistics
            for signal_name in msgs[0].signals:
                values = []
                for msg in msgs:
                    value = msg.get_signal_value(signal_name)
                    if value is not None:
                        values.append(value)

                if values:
                    self.baseline_stats[f"{msg_id}_{signal_name}_mean"] = np.mean(values)
                    self.baseline_stats[f"{msg_id}_{signal_name}_std"] = np.std(values)
                    self.baseline_stats[f"{msg_id}_{signal_name}_min"] = np.min(values)
                    self.baseline_stats[f"{msg_id}_{signal_name}_max"] = np.max(values)

    def get_statistics(self) -> Dict[str, Any]:
        """Get reward calculator statistics"""
        return {
            'explored_states': len(self.explored_states),
            'message_coverage': sum(len(v) for v in self.message_coverage.values()),
            'total_anomalies': len(self.anomaly_history),
            'unique_actions': len(set(self._hash_action(a) for a in self.action_history)),
            'baseline_stats_count': len(self.baseline_stats)
        }

    def reset(self):
        """Reset reward calculator state"""
        self.message_history.clear()
        self.action_history.clear()
        self.explored_states.clear()
        self.message_coverage.clear()
        self.anomaly_history.clear()
        self.messages_since_baseline = 0