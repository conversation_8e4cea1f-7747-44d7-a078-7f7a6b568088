"""
Frequency Domain Feature Extractor
Extracts 8 frequency-domain features from signal time series
"""
import numpy as np
from typing import List, Di<PERSON>, Tu<PERSON>, Optional
from collections import defaultdict, deque
from scipy import signal as scipy_signal
from scipy.fft import fft, fftfreq
import warnings

from simulation.can_message import CANMessage

warnings.filterwarnings('ignore', category=RuntimeWarning)

class FrequencyDomainFeatureExtractor:
    """
    Extracts frequency-domain features including:
    - Dominant frequencies
    - Spectral centroid and spread
    - Power spectral density
    - Frequency band energy
    - Periodicity measures
    """

    def __init__(self, sample_rate: float = 100.0, fft_size: int = 256):
        """
        Initialize frequency domain feature extractor

        Args:
            sample_rate: Assumed sample rate in Hz
            fft_size: Size of FFT window
        """
        self.sample_rate = sample_rate
        self.fft_size = fft_size
        self.signal_buffers = defaultdict(lambda: deque(maxlen=fft_size))
        self.timestamp_buffers = defaultdict(lambda: deque(maxlen=fft_size))

        # Frequency bands for analysis (Hz)
        self.frequency_bands = [
            (0, 10),    # Very low frequency
            (10, 20),   # Low frequency
            (20, 30),   # Medium frequency
            (30, 40),   # High frequency
            (40, 50)    # Very high frequency
        ]

    def update(self, message: CANMessage):
        """Update with new CAN message"""
        timestamp = message.timestamp

        # Store signal values for FFT analysis
        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                self.signal_buffers[key].append(value)
                self.timestamp_buffers[key].append(timestamp)

    def extract(self) -> np.ndarray:
        """
        Extract 8 frequency-domain features

        Returns:
            numpy array of 8 frequency features
        """
        # Find signal with most data
        best_signal = self._get_best_signal_for_analysis()

        if best_signal is None:
            return np.zeros(8, dtype=np.float32)

        features = self._extract_frequency_features(best_signal)

        # Ensure exactly 8 features
        features = features[:8]
        while len(features) < 8:
            features.append(0.0)

        return np.array(features, dtype=np.float32)

    def _get_best_signal_for_analysis(self) -> Optional[str]:
        """Get signal with most data points for frequency analysis"""
        valid_signals = [(k, len(v)) for k, v in self.signal_buffers.items()
                        if len(v) >= self.fft_size // 2]

        if not valid_signals:
            return None

        # Return signal with most samples
        best_signal = max(valid_signals, key=lambda x: x[1])
        return best_signal[0]

    def _extract_frequency_features(self, signal_key: str) -> List[float]:
        """Extract frequency features from a signal"""
        values = np.array(self.signal_buffers[signal_key])
        timestamps = np.array(self.timestamp_buffers[signal_key])

        # Resample to uniform time grid if needed
        uniform_values = self._resample_uniform(values, timestamps)

        if len(uniform_values) < 16:  # Not enough data
            return [0.0] * 8

        # Compute FFT
        fft_result, freqs = self._compute_fft(uniform_values)

        features = []

        # 1. Dominant frequency
        dominant_freq = self._find_dominant_frequency(fft_result, freqs)
        features.append(dominant_freq)

        # 2. Spectral centroid
        spectral_centroid = self._calculate_spectral_centroid(fft_result, freqs)
        features.append(spectral_centroid)

        # 3. Spectral spread
        spectral_spread = self._calculate_spectral_spread(
            fft_result, freqs, spectral_centroid
        )
        features.append(spectral_spread)

        # 4-8. Energy in frequency bands (5 bands)
        band_energies = self._calculate_band_energies(fft_result, freqs)
        features.extend(band_energies[:5])

        return features

    def _resample_uniform(self, values: np.ndarray,
                         timestamps: np.ndarray) -> np.ndarray:
        """Resample to uniform time grid"""
        if len(values) < 2:
            return values

        # Calculate actual sample rate
        time_diffs = np.diff(timestamps)
        avg_sample_rate = 1.0 / np.mean(time_diffs) if np.mean(time_diffs) > 0 else self.sample_rate

        # Create uniform time grid
        duration = timestamps[-1] - timestamps[0]
        num_samples = int(duration * self.sample_rate)

        if num_samples < 2:
            return values

        uniform_time = np.linspace(timestamps[0], timestamps[-1], num_samples)

        # Interpolate values
        uniform_values = np.interp(uniform_time, timestamps, values)

        return uniform_values

    def _compute_fft(self, values: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Compute FFT and return magnitude spectrum"""
        # Apply window to reduce spectral leakage
        window = scipy_signal.windows.hann(len(values))
        windowed_values = values * window

        # Compute FFT
        fft_result = fft(windowed_values, n=self.fft_size)
        freqs = fftfreq(self.fft_size, d=1/self.sample_rate)

        # Get positive frequencies only
        positive_freq_idx = freqs > 0
        freqs = freqs[positive_freq_idx]
        magnitude = np.abs(fft_result[positive_freq_idx])

        return magnitude, freqs

    def _find_dominant_frequency(self, magnitude: np.ndarray,
                               freqs: np.ndarray) -> float:
        """Find the dominant frequency component"""
        if len(magnitude) == 0:
            return 0.0

        # Skip DC component
        if len(magnitude) > 1:
            peak_idx = np.argmax(magnitude[1:]) + 1
            dominant_freq = freqs[peak_idx]
        else:
            dominant_freq = 0.0

        return float(dominant_freq)

    def _calculate_spectral_centroid(self, magnitude: np.ndarray,
                                   freqs: np.ndarray) -> float:
        """Calculate spectral centroid (center of mass)"""
        if len(magnitude) == 0 or np.sum(magnitude) == 0:
            return 0.0

        centroid = np.sum(freqs * magnitude) / np.sum(magnitude)
        return float(centroid)

    def _calculate_spectral_spread(self, magnitude: np.ndarray,
                                 freqs: np.ndarray,
                                 centroid: float) -> float:
        """Calculate spectral spread (standard deviation)"""
        if len(magnitude) == 0 or np.sum(magnitude) == 0:
            return 0.0

        spread = np.sqrt(
            np.sum(((freqs - centroid) ** 2) * magnitude) / np.sum(magnitude)
        )
        return float(spread)

    def _calculate_band_energies(self, magnitude: np.ndarray,
                               freqs: np.ndarray) -> List[float]:
        """Calculate energy in predefined frequency bands"""
        band_energies = []

        for low_freq, high_freq in self.frequency_bands:
            # Find indices within band
            band_mask = (freqs >= low_freq) & (freqs < high_freq)

            if np.any(band_mask):
                # Calculate energy (sum of squared magnitudes)
                band_energy = np.sum(magnitude[band_mask] ** 2)
                band_energies.append(float(band_energy))
            else:
                band_energies.append(0.0)

        # Normalize by total energy
        total_energy = np.sum(magnitude ** 2)
        if total_energy > 0:
            band_energies = [e / total_energy for e in band_energies]

        return band_energies

    def extract_advanced_features(self, signal_key: str) -> Dict[str, float]:
        """Extract advanced frequency domain features"""
        if signal_key not in self.signal_buffers:
            return {}

        values = np.array(self.signal_buffers[signal_key])
        timestamps = np.array(self.timestamp_buffers[signal_key])

        if len(values) < 32:
            return {}

        # Resample and compute FFT
        uniform_values = self._resample_uniform(values, timestamps)
        magnitude, freqs = self._compute_fft(uniform_values)

        features = {}

        # Spectral entropy
        features['spectral_entropy'] = self._calculate_spectral_entropy(magnitude)

        # Spectral rolloff
        features['spectral_rolloff'] = self._calculate_spectral_rolloff(
            magnitude, freqs
        )

        # Spectral flux
        features['spectral_flux'] = self._calculate_spectral_flux(uniform_values)

        # Periodicity strength
        features['periodicity'] = self._calculate_periodicity(uniform_values)

        return features

    def _calculate_spectral_entropy(self, magnitude: np.ndarray) -> float:
        """Calculate spectral entropy"""
        if len(magnitude) == 0 or np.sum(magnitude) == 0:
            return 0.0

        # Normalize to probability distribution
        prob = magnitude / np.sum(magnitude)

        # Calculate entropy
        entropy = -np.sum(prob * np.log2(prob + 1e-10))

        # Normalize by maximum possible entropy
        max_entropy = np.log2(len(magnitude))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0

        return float(normalized_entropy)

    def _calculate_spectral_rolloff(self, magnitude: np.ndarray,
                                  freqs: np.ndarray,
                                  threshold: float = 0.85) -> float:
        """Calculate spectral rolloff frequency"""
        if len(magnitude) == 0:
            return 0.0

        # Cumulative sum of energy
        cumsum_energy = np.cumsum(magnitude ** 2)
        total_energy = cumsum_energy[-1]

        if total_energy == 0:
            return 0.0

        # Find frequency where threshold is reached
        rolloff_idx = np.where(cumsum_energy >= threshold * total_energy)[0]

        if len(rolloff_idx) > 0:
            return float(freqs[rolloff_idx[0]])
        else:
            return float(freqs[-1])

    def _calculate_spectral_flux(self, values: np.ndarray,
                               window_size: int = 32) -> float:
        """Calculate spectral flux (rate of change)"""
        if len(values) < window_size * 2:
            return 0.0

        flux_values = []

        for i in range(window_size, len(values) - window_size, window_size // 2):
            # FFT of current and previous windows
            curr_window = values[i:i+window_size]
            prev_window = values[i-window_size:i]

            curr_fft = np.abs(fft(curr_window))
            prev_fft = np.abs(fft(prev_window))

            # Spectral flux
            flux = np.sum((curr_fft - prev_fft) ** 2)
            flux_values.append(flux)

        return float(np.mean(flux_values)) if flux_values else 0.0

    def _calculate_periodicity(self, values: np.ndarray) -> float:
        """Calculate periodicity strength using autocorrelation"""
        if len(values) < 50:
            return 0.0

        # Normalize values
        values_norm = (values - np.mean(values)) / (np.std(values) + 1e-10)

        # Compute autocorrelation
        autocorr = np.correlate(values_norm, values_norm, mode='full')
        autocorr = autocorr[len(autocorr)//2:]

        # Find peaks in autocorrelation (excluding lag 0)
        peaks = []
        for i in range(10, len(autocorr)//2):
            if (autocorr[i] > autocorr[i-1] and
                autocorr[i] > autocorr[i+1] and
                autocorr[i] > 0.3):
                peaks.append((i, autocorr[i]))

        if peaks:
            # Return strength of strongest peak
            strongest_peak = max(peaks, key=lambda x: x[1])
            return float(strongest_peak[1])

        return 0.0

    def get_feature_names(self) -> List[str]:
        """Get names for frequency domain features"""
        return [
            'freq_dominant',
            'freq_spectral_centroid',
            'freq_spectral_spread',
            'freq_band_0_10Hz',
            'freq_band_10_20Hz',
            'freq_band_20_30Hz',
            'freq_band_30_40Hz',
            'freq_band_40_50Hz'
        ]

    def reset(self):
        """Reset the feature extractor"""
        self.signal_buffers.clear()
        self.timestamp_buffers.clear()