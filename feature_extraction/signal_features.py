"""
Signal-Level Feature Extractor
Extracts 20 signal-level features from decoded CAN signals
"""
import numpy as np
from typing import List, Dict, Deque, <PERSON><PERSON>, Optional, Any
from collections import defaultdict, deque
from scipy import stats
import warnings

from simulation.can_message import CANMessage

warnings.filterwarnings('ignore', category=RuntimeWarning)

class SignalLevelFeatureExtractor:
    """
    Extracts signal-level features including:
    - Statistical moments (mean, variance, skewness, kurtosis)
    - Range analysis (min, max, range)
    - Rate of change
    - Signal correlations
    """

    def __init__(self, max_signals: int = 50, window_size: int = 100):
        """
        Initialize signal-level feature extractor

        Args:
            max_signals: Maximum number of signals to track
            window_size: Size of sliding window for calculations
        """
        self.max_signals = max_signals
        self.window_size = window_size
        self.signal_values = defaultdict(lambda: deque(maxlen=window_size))
        self.signal_timestamps = defaultdict(lambda: deque(maxlen=window_size))
        self.signal_metadata = {}

    def update(self, message: CANMessage):
        """Update with new CAN message"""
        timestamp = message.timestamp

        # Extract all signal values
        for signal_name, signal_def in message.signals.items():
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"

                # Store value and timestamp
                self.signal_values[key].append(value)
                self.signal_timestamps[key].append(timestamp)

                # Store signal metadata
                if key not in self.signal_metadata:
                    self.signal_metadata[key] = {
                        'min_value': signal_def.min_value,
                        'max_value': signal_def.max_value,
                        'scale': signal_def.scale,
                        'offset': signal_def.offset,
                        'unit': signal_def.unit
                    }

    def extract(self) -> np.ndarray:
        """
        Extract 20 signal-level features

        Returns:
            numpy array of 20 signal features
        """
        features = []

        # Get top signals by update frequency
        signal_keys = self._get_top_signals(5)

        # Extract features for each top signal (4 features × 5 signals = 20)
        for key in signal_keys:
            signal_features = self._extract_signal_features(key)
            features.extend(signal_features)

        # Pad if necessary
        while len(features) < 20:
            features.extend([0.0, 0.0, 0.0, 0.0])

        return np.array(features[:20], dtype=np.float32)

    def _get_top_signals(self, n: int) -> List[str]:
        """Get top N signals by update frequency"""
        sorted_signals = sorted(
            self.signal_values.keys(),
            key=lambda k: len(self.signal_values[k]),
            reverse=True
        )
        return sorted_signals[:n]

    def _extract_signal_features(self, signal_key: str) -> List[float]:
        """Extract features for a single signal"""
        values = list(self.signal_values[signal_key])

        if len(values) < 2:
            return [0.0, 0.0, 0.0, 0.0]

        values_array = np.array(values)

        # 1. Mean
        mean_val = np.mean(values_array)

        # 2. Variance
        var_val = np.var(values_array)

        # 3. Range (normalized)
        metadata = self.signal_metadata.get(signal_key, {})
        expected_range = metadata.get('max_value', 100) - metadata.get('min_value', 0)
        actual_range = np.max(values_array) - np.min(values_array)
        normalized_range = actual_range / max(expected_range, 1.0)

        # 4. Rate of change
        if len(values) > 1:
            diffs = np.diff(values_array)
            rate_of_change = np.mean(np.abs(diffs))
        else:
            rate_of_change = 0.0

        return [mean_val, var_val, normalized_range, rate_of_change]

    def extract_advanced(self) -> Dict[str, np.ndarray]:
        """Extract advanced signal features including correlations"""
        features = {}

        # Basic statistics for all signals
        all_stats = self._calculate_all_statistics()
        features['statistics'] = all_stats

        # Correlation matrix
        correlation_matrix = self._calculate_correlations()
        features['correlations'] = correlation_matrix

        # Anomaly scores
        anomaly_scores = self._calculate_anomaly_scores()
        features['anomaly_scores'] = anomaly_scores

        return features

    def _calculate_all_statistics(self) -> np.ndarray:
        """Calculate comprehensive statistics for all signals"""
        stats_list = []

        for signal_key, values in self.signal_values.items():
            if len(values) > 1:
                values_array = np.array(values)
                stats_list.append({
                    'key': signal_key,
                    'mean': np.mean(values_array),
                    'std': np.std(values_array),
                    'min': np.min(values_array),
                    'max': np.max(values_array),
                    'skewness': stats.skew(values_array),
                    'kurtosis': stats.kurtosis(values_array)
                })

        return stats_list

    def _calculate_correlations(self) -> np.ndarray:
        """Calculate correlation matrix between signals"""
        # Select signals with enough data
        valid_signals = [(k, v) for k, v in self.signal_values.items()
                        if len(v) >= 10]

        if len(valid_signals) < 2:
            return np.array([[]])

        # Limit to top signals
        valid_signals = valid_signals[:min(20, len(valid_signals))]

        # Build value matrix
        min_length = min(len(v) for _, v in valid_signals)
        value_matrix = np.zeros((min_length, len(valid_signals)))

        for i, (_, values) in enumerate(valid_signals):
            value_matrix[:, i] = list(values)[-min_length:]

        # Calculate correlation matrix
        if value_matrix.shape[0] > 1:
            correlation_matrix = np.corrcoef(value_matrix.T)
        else:
            correlation_matrix = np.eye(len(valid_signals))

        return correlation_matrix

    def _calculate_anomaly_scores(self) -> Dict[str, float]:
        """Calculate anomaly scores for each signal"""
        anomaly_scores = {}

        for signal_key, values in self.signal_values.items():
            if len(values) > 10:
                values_array = np.array(values)

                # Calculate z-scores for recent values
                mean = np.mean(values_array[:-5])  # Exclude recent values
                std = np.std(values_array[:-5])

                if std > 0:
                    recent_values = values_array[-5:]
                    z_scores = np.abs((recent_values - mean) / std)
                    max_z_score = np.max(z_scores)
                    anomaly_scores[signal_key] = float(max_z_score)
                else:
                    anomaly_scores[signal_key] = 0.0

        return anomaly_scores

    def get_signal_info(self, signal_key: str) -> Dict[str, Any]:
        """Get detailed information about a specific signal"""
        if signal_key not in self.signal_values:
            return {}

        values = list(self.signal_values[signal_key])
        timestamps = list(self.signal_timestamps[signal_key])

        if not values:
            return {}

        values_array = np.array(values)

        info = {
            'signal_key': signal_key,
            'num_samples': len(values),
            'statistics': {
                'mean': float(np.mean(values_array)),
                'std': float(np.std(values_array)),
                'min': float(np.min(values_array)),
                'max': float(np.max(values_array))
            }
        }

        # Add metadata if available
        if signal_key in self.signal_metadata:
            info['metadata'] = self.signal_metadata[signal_key]

        # Calculate update frequency
        if len(timestamps) > 1:
            time_diffs = np.diff(timestamps)
            info['update_frequency'] = 1.0 / np.mean(time_diffs)

        return info

    def get_feature_names(self) -> List[str]:
        """Get names for signal-level features"""
        names = []
        for i in range(5):
            names.extend([
                f'signal_{i}_mean',
                f'signal_{i}_variance',
                f'signal_{i}_range',
                f'signal_{i}_rate_of_change'
            ])
        return names[:20]

    def reset(self):
        """Reset the feature extractor"""
        self.signal_values.clear()
        self.signal_timestamps.clear()
        self.signal_metadata.clear()


class SignalPatternDetector:
    """Detects patterns in signal behavior"""

    def __init__(self):
        self.patterns = {
            'constant': self._detect_constant,
            'periodic': self._detect_periodic,
            'trending': self._detect_trending,
            'noisy': self._detect_noisy,
            'stepped': self._detect_stepped
        }

    def detect_patterns(self, values: List[float]) -> Dict[str, float]:
        """Detect various patterns in signal values"""
        if len(values) < 10:
            return {pattern: 0.0 for pattern in self.patterns}

        results = {}
        values_array = np.array(values)

        for pattern_name, detector_func in self.patterns.items():
            confidence = detector_func(values_array)
            results[pattern_name] = confidence

        return results

    def _detect_constant(self, values: np.ndarray) -> float:
        """Detect if signal is constant"""
        if np.std(values) < 0.01 * np.abs(np.mean(values)):
            return 1.0
        return 0.0

    def _detect_periodic(self, values: np.ndarray) -> float:
        """Detect periodic patterns"""
        # Simple autocorrelation check
        if len(values) < 20:
            return 0.0

        # Compute autocorrelation
        mean = np.mean(values)
        var = np.var(values)
        if var == 0:
            return 0.0

        normalized = (values - mean) / np.sqrt(var)
        autocorr = np.correlate(normalized, normalized, mode='full')
        autocorr = autocorr[len(autocorr)//2:]

        # Look for peaks in autocorrelation
        peaks = []
        for i in range(2, len(autocorr)//2):
            if autocorr[i] > 0.5 and autocorr[i] > autocorr[i-1] and autocorr[i] > autocorr[i+1]:
                peaks.append(i)

        if peaks:
            return min(1.0, len(peaks) * 0.3)
        return 0.0

    def _detect_trending(self, values: np.ndarray) -> float:
        """Detect trending behavior"""
        # Linear regression
        x = np.arange(len(values))
        slope, _, r_value, _, _ = stats.linregress(x, values)

        # Strong trend if R² > 0.7
        if r_value ** 2 > 0.7:
            return r_value ** 2
        return 0.0

    def _detect_noisy(self, values: np.ndarray) -> float:
        """Detect noisy signal"""
        # High frequency variation
        if len(values) < 3:
            return 0.0

        diffs = np.diff(values)
        noise_ratio = np.std(diffs) / (np.std(values) + 1e-10)

        return min(1.0, noise_ratio)

    def _detect_stepped(self, values: np.ndarray) -> float:
        """Detect stepped/discrete changes"""
        unique_values = np.unique(values)

        if len(unique_values) < len(values) / 5:  # Few unique values
            # Check for sudden changes
            diffs = np.abs(np.diff(values))
            if np.sum(diffs > 0) < len(diffs) / 3:  # Infrequent changes
                return 0.8

        return 0.0