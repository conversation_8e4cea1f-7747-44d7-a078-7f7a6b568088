"""
Entropy Feature Extractor
Extracts 16 entropy-based features from CAN messages
"""
import numpy as np
from typing import List, Dict, Deque, Any
from collections import defaultdict, deque, Counter
from scipy import stats

from simulation.can_message import CANMessage

class EntropyFeatureExtractor:
    """
    Extracts entropy-based features including:
    - Message ID entropy
    - Payload entropy (byte and bit level)
    - Signal entropy
    - Conditional entropy
    """

    def __init__(self, window_size: int = 100):
        """
        Initialize entropy feature extractor

        Args:
            window_size: Size of sliding window for entropy calculations
        """
        self.window_size = window_size
        self.message_buffer = deque(maxlen=window_size)
        self.id_counts = defaultdict(int)
        self.payload_bytes = defaultdict(lambda: defaultdict(int))
        self.signal_values = defaultdict(lambda: deque(maxlen=window_size))
        self.bit_patterns = defaultdict(lambda: deque(maxlen=window_size))

    def update(self, message: CANMessage):
        """Update with new CAN message"""
        self.message_buffer.append(message)
        self.id_counts[message.id] += 1

        # Track payload byte distributions
        for i, byte in enumerate(message.data):
            self.payload_bytes[i][byte] += 1

        # Track signal value distributions
        for signal_name in message.signals:
            value = message.get_signal_value(signal_name)
            if value is not None:
                key = f"{message.id:03X}_{signal_name}"
                self.signal_values[key].append(value)

        # Track bit patterns
        bit_string = ''.join(format(byte, '08b') for byte in message.data)
        self.bit_patterns[message.id].append(bit_string)

    def extract(self) -> np.ndarray:
        """
        Extract 16 entropy features

        Returns:
            numpy array of 16 entropy features
        """
        features = []

        # 1. Message ID entropy (1 feature)
        id_entropy = self._calculate_id_entropy()
        features.append(id_entropy)

        # 2. Payload byte entropy (8 features - one per byte)
        byte_entropies = self._calculate_byte_entropies()
        features.extend(byte_entropies[:8])

        # 3. Bit-level entropy (4 features - for first 32 bits)
        bit_entropies = self._calculate_bit_entropies()
        features.extend(bit_entropies[:4])

        # 4. Signal value entropy (2 features - top 2 signals)
        signal_entropies = self._calculate_signal_entropies()
        features.extend(signal_entropies[:2])

        # 5. Conditional entropy (1 feature)
        conditional_entropy = self._calculate_conditional_entropy()
        features.append(conditional_entropy)

        # Ensure exactly 16 features
        features = features[:16]
        while len(features) < 16:
            features.append(0.0)

        return np.array(features, dtype=np.float32)

    def _calculate_entropy(self, counts: List[int]) -> float:
        """Calculate Shannon entropy from count distribution"""
        if not counts or sum(counts) == 0:
            return 0.0

        total = sum(counts)
        probs = [c / total for c in counts if c > 0]
        entropy = -sum(p * np.log2(p) for p in probs)

        return entropy

    def _calculate_id_entropy(self) -> float:
        """Calculate message ID entropy"""
        return self._calculate_entropy(list(self.id_counts.values()))

    def _calculate_byte_entropies(self) -> List[float]:
        """Calculate entropy for each byte position"""
        entropies = []

        for byte_pos in range(8):
            if byte_pos in self.payload_bytes:
                byte_counts = list(self.payload_bytes[byte_pos].values())
                entropy = self._calculate_entropy(byte_counts)
                entropies.append(entropy)
            else:
                entropies.append(0.0)

        return entropies

    def _calculate_bit_entropies(self) -> List[float]:
        """Calculate bit-level entropy for specific bit positions"""
        bit_entropies = []

        # Analyze first 32 bits (4 bytes) in groups of 8
        for byte_idx in range(4):
            bit_counts = defaultdict(int)

            for msg in self.message_buffer:
                if byte_idx < len(msg.data):
                    byte_val = msg.data[byte_idx]
                    for bit_idx in range(8):
                        bit_val = (byte_val >> bit_idx) & 1
                        bit_counts[bit_idx * 2 + bit_val] += 1

            # Calculate entropy for this byte's bits
            if bit_counts:
                entropy = self._calculate_entropy(list(bit_counts.values()))
                bit_entropies.append(entropy)
            else:
                bit_entropies.append(0.0)

        return bit_entropies

    def _calculate_signal_entropies(self) -> List[float]:
        """Calculate entropy of signal values"""
        signal_entropies = []

        # Sort signals by number of samples
        sorted_signals = sorted(self.signal_values.items(),
                              key=lambda x: len(x[1]), reverse=True)

        for signal_key, values in sorted_signals[:5]:  # Top 5 signals
            if len(values) > 10:
                # Discretize continuous values into bins
                if all(isinstance(v, (int, float)) for v in values):
                    hist, _ = np.histogram(values, bins=10)
                    entropy = self._calculate_entropy(hist.tolist())
                else:
                    # Categorical values
                    value_counts = Counter(values)
                    entropy = self._calculate_entropy(list(value_counts.values()))

                signal_entropies.append(entropy)

        return signal_entropies

    def _calculate_conditional_entropy(self) -> float:
        """Calculate conditional entropy H(ID_next | ID_current)"""
        if len(self.message_buffer) < 2:
            return 0.0

        # Build transition counts
        transitions = defaultdict(lambda: defaultdict(int))

        for i in range(1, len(self.message_buffer)):
            prev_id = self.message_buffer[i-1].id
            curr_id = self.message_buffer[i].id
            transitions[prev_id][curr_id] += 1

        # Calculate conditional entropy
        conditional_entropy = 0.0
        total_transitions = len(self.message_buffer) - 1

        for prev_id, next_counts in transitions.items():
            prev_prob = sum(next_counts.values()) / total_transitions

            # Entropy of next ID given this previous ID
            h_next_given_prev = self._calculate_entropy(list(next_counts.values()))
            conditional_entropy += prev_prob * h_next_given_prev

        return conditional_entropy

    def get_feature_names(self) -> List[str]:
        """Get names for entropy features"""
        return [
            'entropy_message_id',
            'entropy_byte_0', 'entropy_byte_1', 'entropy_byte_2', 'entropy_byte_3',
            'entropy_byte_4', 'entropy_byte_5', 'entropy_byte_6', 'entropy_byte_7',
            'entropy_bits_0_7', 'entropy_bits_8_15', 'entropy_bits_16_23', 'entropy_bits_24_31',
            'entropy_signal_0', 'entropy_signal_1',
            'entropy_conditional'
        ][:16]

    def reset(self):
        """Reset the feature extractor"""
        self.message_buffer.clear()
        self.id_counts.clear()
        self.payload_bytes.clear()
        self.signal_values.clear()
        self.bit_patterns.clear()


class AdvancedEntropyMetrics:
    """Advanced entropy-based metrics for deeper analysis"""

    @staticmethod
    def calculate_joint_entropy(values1: List[float], values2: List[float]) -> float:
        """Calculate joint entropy H(X,Y)"""
        if len(values1) != len(values2) or not values1:
            return 0.0

        # Create joint distribution
        joint_counts = defaultdict(int)
        for v1, v2 in zip(values1, values2):
            joint_counts[(v1, v2)] += 1

        # Calculate entropy
        total = len(values1)
        entropy = 0.0
        for count in joint_counts.values():
            if count > 0:
                p = count / total
                entropy -= p * np.log2(p)

        return entropy

    @staticmethod
    def calculate_mutual_information(values1: List[float], values2: List[float]) -> float:
        """Calculate mutual information I(X;Y) = H(X) + H(Y) - H(X,Y)"""
        if len(values1) != len(values2) or not values1:
            return 0.0

        # Individual entropies
        h_x = EntropyFeatureExtractor._calculate_entropy(
            None, list(Counter(values1).values())
        )
        h_y = EntropyFeatureExtractor._calculate_entropy(
            None, list(Counter(values2).values())
        )

        # Joint entropy
        h_xy = AdvancedEntropyMetrics.calculate_joint_entropy(values1, values2)

        return h_x + h_y - h_xy

    @staticmethod
    def calculate_relative_entropy(p_dist: Dict[Any, float],
                                 q_dist: Dict[Any, float]) -> float:
        """Calculate Kullback-Leibler divergence D_KL(P||Q)"""
        kl_div = 0.0

        for key in p_dist:
            p = p_dist[key]
            q = q_dist.get(key, 1e-10)  # Small epsilon to avoid log(0)

            if p > 0:
                kl_div += p * np.log2(p / q)

        return kl_div