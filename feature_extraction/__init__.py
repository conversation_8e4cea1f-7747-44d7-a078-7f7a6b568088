"""
AMCFF-RL Feature Extraction Module
Multi-modal feature extraction for CAN bus messages
"""

from typing import Dict, List, Optional, Tuple, Union
import numpy as np

# Import all feature extraction components
from .timing_features import TimingFeatureExtractor
from .entropy_features import EntropyFeatureExtractor
from .signal_features import SignalLevelFeatureExtractor as SignalFeatureExtractor
from .contextual_features import ContextualFeatureExtractor
from .frequency_features import FrequencyDomainFeatureExtractor as FrequencyFeatureExtractor
from .feature_extractor import MultiModalFeatureExtractor

# Define feature dimensions
FEATURE_DIMENSIONS = {
    'signal': 20,
    'bit': 16,
    'temporal': 15,
    'protocol': 12,
    'anomaly': 15,
    'total': 78
}

# Feature normalization ranges
FEATURE_RANGES = {
    'timing': (0, 1000),  # milliseconds
    'entropy': (0, 8),    # bits
    'frequency': (0, 1000),  # Hz
    'signal': (-1e6, 1e6),  # signal values
    'correlation': (-1, 1)  # correlation coefficient
}

class FeatureVector:
    """Container for the 78-dimensional feature vector"""

    def __init__(self):
        self.features = np.zeros(FEATURE_DIMENSIONS['total'])
        self.feature_names = []
        self.feature_groups = {
            'signal': slice(0, 20),
            'bit': slice(20, 36),
            'temporal': slice(36, 51),
            'protocol': slice(51, 63),
            'anomaly': slice(63, 78)
        }
        self._initialize_feature_names()

    def _initialize_feature_names(self):
        """Initialize feature names for interpretability"""
        # Signal features (20)
        signal_features = [
            'mean_signal_value', 'std_signal_value', 'min_signal_value', 'max_signal_value',
            'signal_range', 'signal_skewness', 'signal_kurtosis', 'zero_crossing_rate',
            'signal_energy', 'signal_entropy', 'dominant_frequency', 'spectral_centroid',
            'spectral_spread', 'spectral_flux', 'spectral_rolloff', 'signal_autocorr',
            'signal_trend', 'signal_seasonality', 'signal_noise', 'signal_complexity'
        ]

        # Bit-level features (16)
        bit_features = [
            'bit_transition_rate', 'bit_entropy', 'hamming_weight', 'bit_runs',
            'longest_bit_run', 'bit_alternation', 'byte_entropy', 'nibble_entropy',
            'bit_correlation', 'bit_randomness', 'stuffing_bits', 'bit_density',
            'bit_pattern_score', 'bit_symmetry', 'bit_clustering', 'bit_dispersion'
        ]

        # Temporal features (15)
        temporal_features = [
            'mean_inter_arrival', 'std_inter_arrival', 'min_inter_arrival', 'max_inter_arrival',
            'jitter', 'burst_ratio', 'periodicity_score', 'timing_entropy',
            'phase_stability', 'frequency_stability', 'temporal_correlation',
            'timing_anomaly_score', 'message_rate', 'rate_variance', 'timing_predictability'
        ]

        # Protocol features (12)
        protocol_features = [
            'dlc_consistency', 'id_validity', 'crc_validity', 'ack_rate',
            'error_frame_rate', 'stuff_bit_violations', 'form_violations',
            'bit_timing_violations', 'arbitration_losses', 'bus_off_events',
            'error_passive_events', 'protocol_compliance_score'
        ]

        # Anomaly features (15)
        anomaly_features = [
            'timing_anomaly', 'value_anomaly', 'sequence_anomaly', 'frequency_anomaly',
            'protocol_anomaly', 'statistical_anomaly', 'contextual_anomaly',
            'collective_anomaly', 'point_anomaly', 'pattern_anomaly',
            'anomaly_persistence', 'anomaly_severity', 'anomaly_confidence',
            'anomaly_type_diversity', 'overall_anomaly_score'
        ]

        self.feature_names = (signal_features + bit_features + temporal_features +
                            protocol_features + anomaly_features)

    def set_group(self, group: str, values: np.ndarray):
        """Set features for a specific group"""
        if group not in self.feature_groups:
            raise ValueError(f"Unknown feature group: {group}")

        group_slice = self.feature_groups[group]
        expected_size = group_slice.stop - group_slice.start

        if len(values) != expected_size:
            raise ValueError(f"Expected {expected_size} features for {group}, got {len(values)}")

        self.features[group_slice] = values

    def get_group(self, group: str) -> np.ndarray:
        """Get features for a specific group"""
        if group not in self.feature_groups:
            raise ValueError(f"Unknown feature group: {group}")

        return self.features[self.feature_groups[group]]

    def normalize(self, scaler=None):
        """Normalize feature vector"""
        if scaler is None:
            # Simple min-max normalization
            for group, group_slice in self.feature_groups.items():
                group_features = self.features[group_slice]
                min_val = np.min(group_features)
                max_val = np.max(group_features)
                if max_val > min_val:
                    self.features[group_slice] = (group_features - min_val) / (max_val - min_val)
        else:
            # Use provided scaler
            self.features = scaler.transform(self.features.reshape(1, -1)).flatten()

    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary with feature names"""
        return dict(zip(self.feature_names, self.features))

    def __repr__(self):
        return f"FeatureVector(shape={self.features.shape}, groups={list(self.feature_groups.keys())})"

# Factory functions
def create_feature_extractor(config=None) -> MultiModalFeatureExtractor:
    """Create a configured multi-modal feature extractor"""
    if config is None:
        from ..config import feature_config
        config = feature_config

    return MultiModalFeatureExtractor(
        timing_extractor=TimingFeatureExtractor(config),
        entropy_extractor=EntropyFeatureExtractor(config),
        signal_extractor=SignalFeatureExtractor(config),
        contextual_extractor=ContextualFeatureExtractor(config),
        frequency_extractor=FrequencyFeatureExtractor(config)
    )

def extract_features(messages: List[Dict], config=None) -> FeatureVector:
    """Extract features from a list of CAN messages"""
    extractor = create_feature_extractor(config)
    return extractor.extract(messages)

# Validation functions
def validate_feature_vector(features: np.ndarray) -> bool:
    """Validate that feature vector has correct dimensions and values"""
    if len(features) != FEATURE_DIMENSIONS['total']:
        return False

    # Check for NaN or infinite values
    if np.any(np.isnan(features)) or np.any(np.isinf(features)):
        return False

    return True

def get_feature_importance(model=None) -> Dict[str, float]:
    """Get feature importance scores"""
    if model is None:
        # Return uniform importance if no model provided
        return {name: 1.0 / FEATURE_DIMENSIONS['total']
                for name in FeatureVector().feature_names}

    # Extract importance from model (implementation depends on model type)
    # This is a placeholder
    return {}

# Export main components
__all__ = [
    'FeatureVector',
    'FEATURE_DIMENSIONS',
    'FEATURE_RANGES',
    'create_feature_extractor',
    'extract_features',
    'validate_feature_vector',
    'get_feature_importance',
    'TimingFeatureExtractor',
    'EntropyFeatureExtractor',
    'SignalFeatureExtractor',
    'ContextualFeatureExtractor',
    'FrequencyFeatureExtractor',
    'MultiModalFeatureExtractor'
]