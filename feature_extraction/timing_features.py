"""
Timing Feature Extractor
Extracts 24 timing-based features from CAN messages
"""
import numpy as np
from typing import List, Dict, Deque
from collections import defaultdict, deque
from scipy import stats

from simulation.can_message import CANMessage

class TimingFeatureExtractor:
    """
    Extracts timing-based features including:
    - Inter-arrival times (IAT) statistics
    - Frequency variations
    - Burst patterns
    - Sequence analysis
    """
    
    def __init__(self, windows: List[float] = None):
        """
        Initialize timing feature extractor
        
        Args:
            windows: Time windows for analysis [0.05, 0.1, 0.5, 1.0, 5.0] seconds
        """
        self.windows = windows or [0.05, 0.1, 0.5, 1.0, 5.0]
        self.message_times = defaultdict(lambda: deque(maxlen=1000))
        self.inter_arrival_times = defaultdict(lambda: deque(maxlen=999))
        self.burst_detector = BurstDetector()
        self.sequence_analyzer = SequenceAnalyzer()
        
    def update(self, message: CANMessage):
        """Update with new CAN message"""
        msg_id = message.id
        timestamp = message.timestamp
        
        # Store timestamp
        self.message_times[msg_id].append(timestamp)
        
        # Calculate inter-arrival time
        if len(self.message_times[msg_id]) > 1:
            iat = timestamp - self.message_times[msg_id][-2]
            self.inter_arrival_times[msg_id].append(iat)
            
        # Update burst detector
        self.burst_detector.update(msg_id, timestamp)
        
        # Update sequence analyzer
        self.sequence_analyzer.update(msg_id, timestamp)
    
    def extract(self, current_time: float) -> np.ndarray:
        """
        Extract 24 timing features
        
        Returns:
            numpy array of 24 timing features
        """
        features = []
        
        # Extract features for each time window (4 features × 5 windows = 20 features)
        for window in self.windows:
            window_features = self._extract_window_features(
                current_time - window, current_time
            )
            features.extend(window_features)
        
        # Add burst features (2 features)
        burst_features = self.burst_detector.get_features()
        features.extend(burst_features[:2])
        
        # Add sequence features (2 features)
        sequence_features = self.sequence_analyzer.get_features()
        features.extend(sequence_features[:2])
        
        # Ensure exactly 24 features
        features = features[:24]
        while len(features) < 24:
            features.append(0.0)
            
        return np.array(features, dtype=np.float32)
    
    def _extract_window_features(self, start_time: float, end_time: float) -> List[float]:
        """Extract features for a specific time window"""
        all_iats = []
        message_counts = defaultdict(int)
        
        # Collect IATs in window
        for msg_id, times in self.message_times.items():
            window_times = [t for t in times if start_time <= t <= end_time]
            message_counts[msg_id] = len(window_times)
            
            if msg_id in self.inter_arrival_times:
                # Get IATs for this window
                window_iats = []
                for i, t in enumerate(times):
                    if start_time <= t <= end_time and i > 0:
                        if i-1 < len(self.inter_arrival_times[msg_id]):
                            window_iats.append(self.inter_arrival_times[msg_id][i-1])
                all_iats.extend(window_iats)
        
        # Calculate statistics
        if all_iats:
            return [
                np.mean(all_iats),                    # Mean IAT
                np.var(all_iats),                     # Variance IAT
                float(stats.skew(all_iats)),          # Skewness
                float(stats.kurtosis(all_iats))       # Kurtosis
            ]
        else:
            return [0.0, 0.0, 0.0, 0.0]
    
    def reset(self):
        """Reset the feature extractor"""
        self.message_times.clear()
        self.inter_arrival_times.clear()
        self.burst_detector.reset()
        self.sequence_analyzer.reset()


class BurstDetector:
    """Detects burst patterns in CAN traffic"""
    
    def __init__(self, burst_threshold: float = 0.01):
        """
        Initialize burst detector
        
        Args:
            burst_threshold: Time threshold for burst detection (10ms default)
        """
        self.burst_threshold = burst_threshold
        self.burst_counts = defaultdict(int)
        self.last_burst_time = defaultdict(float)
        self.current_burst_length = defaultdict(int)
        
    def update(self, msg_id: int, timestamp: float):
        """Update burst detection with new message"""
        if msg_id in self.last_burst_time:
            time_diff = timestamp - self.last_burst_time[msg_id]
            
            if time_diff < self.burst_threshold:
                # Continue burst
                self.current_burst_length[msg_id] += 1
            else:
                # End burst
                if self.current_burst_length[msg_id] > 2:
                    self.burst_counts[msg_id] += 1
                self.current_burst_length[msg_id] = 1
                
        self.last_burst_time[msg_id] = timestamp
    
    def get_features(self) -> List[float]:
        """Get burst-related features"""
        if not self.burst_counts:
            return [0.0, 0.0]
            
        total_bursts = sum(self.burst_counts.values())
        avg_burst_length = np.mean(list(self.current_burst_length.values()))
        
        return [float(total_bursts), float(avg_burst_length)]
    
    def reset(self):
        """Reset burst detector"""
        self.burst_counts.clear()
        self.last_burst_time.clear()
        self.current_burst_length.clear()


class SequenceAnalyzer:
    """Analyzes message sequence patterns"""
    
    def __init__(self, sequence_length: int = 3):
        """
        Initialize sequence analyzer
        
        Args:
            sequence_length: Length of sequences to analyze
        """
        self.sequence_length = sequence_length
        self.message_sequence = deque(maxlen=100)
        self.sequence_counts = defaultdict(int)
        self.transition_matrix = defaultdict(lambda: defaultdict(int))
        
    def update(self, msg_id: int, timestamp: float):
        """Update sequence analysis with new message"""
        self.message_sequence.append((msg_id, timestamp))
        
        # Update sequence counts
        if len(self.message_sequence) >= self.sequence_length:
            sequence = tuple(m[0] for m in list(self.message_sequence)[-self.sequence_length:])
            self.sequence_counts[sequence] += 1
            
        # Update transition matrix
        if len(self.message_sequence) >= 2:
            prev_msg = self.message_sequence[-2][0]
            curr_msg = self.message_sequence[-1][0]
            self.transition_matrix[prev_msg][curr_msg] += 1
    
    def get_features(self) -> List[float]:
        """Get sequence-related features"""
        if not self.sequence_counts:
            return [0.0, 0.0]
            
        # Number of unique sequences
        num_unique_sequences = len(self.sequence_counts)
        
        # Sequence entropy
        total_sequences = sum(self.sequence_counts.values())
        if total_sequences > 0:
            probabilities = [count / total_sequences for count in self.sequence_counts.values()]
            entropy = -sum(p * np.log2(p) if p > 0 else 0 for p in probabilities)
        else:
            entropy = 0.0
            
        return [float(num_unique_sequences), float(entropy)]
    
    def reset(self):
        """Reset sequence analyzer"""
        self.message_sequence.clear()
        self.sequence_counts.clear()
        self.transition_matrix.clear()