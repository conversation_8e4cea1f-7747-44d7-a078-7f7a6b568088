"""
Contextual Feature Extractor
Extracts 10 contextual features related to vehicle state and environment
"""
import numpy as np
from typing import List, Dict, Deque, Optional
from collections import defaultdict, deque
from enum import Enum

from simulation.can_message import CANMessage

class VehicleOperationalMode(Enum):
    """Vehicle operational modes"""
    IDLE = 0
    STARTING = 1
    ACCELERATING = 2
    CRUISING = 3
    BRAKING = 4
    TURNING = 5
    PARKING = 6
    ERROR = 7

class ContextualFeatureExtractor:
    """
    Extracts contextual features including:
    - Vehicle operational state
    - System status indicators
    - Environmental factors
    - State transition patterns
    """

    def __init__(self, history_size: int = 100):
        """
        Initialize contextual feature extractor

        Args:
            history_size: Size of state history to maintain
        """
        self.history_size = history_size
        self.state_history = deque(maxlen=history_size)
        self.mode_history = deque(maxlen=history_size)
        self.transition_counts = defaultdict(lambda: defaultdict(int))

        # Vehicle state tracking
        self.current_mode = VehicleOperationalMode.IDLE
        self.vehicle_state = {
            'speed': 0.0,
            'rpm': 0.0,
            'throttle': 0.0,
            'brake_pressure': 0.0,
            'steering_angle': 0.0,
            'gear': 0,
            'engine_temp': 20.0,
            'systems_active': set()
        }

        # Environmental factors
        self.environmental_factors = {
            'time_of_day': 12.0,  # 24-hour format
            'ambient_temp': 20.0,  # Celsius
            'road_condition': 'dry',  # dry, wet, snow, ice
            'traffic_density': 'medium'  # low, medium, high
        }

    def update(self, message: CANMessage,
              vehicle_state: Optional[Dict[str, float]] = None,
              env_factors: Optional[Dict[str, any]] = None):
        """Update with new message and context"""
        # Update vehicle state from message
        self._update_from_message(message)

        # Update with external vehicle state if provided
        if vehicle_state:
            self.vehicle_state.update(vehicle_state)

        # Update environmental factors
        if env_factors:
            self.environmental_factors.update(env_factors)

        # Determine operational mode
        new_mode = self._determine_operational_mode()

        # Track mode transitions
        if self.current_mode != new_mode:
            self.transition_counts[self.current_mode][new_mode] += 1
            self.current_mode = new_mode

        # Update history
        self.state_history.append(self.vehicle_state.copy())
        self.mode_history.append(self.current_mode)

    def extract(self) -> np.ndarray:
        """
        Extract 10 contextual features

        Returns:
            numpy array of 10 contextual features
        """
        features = []

        # 1-5: One-hot encoding of current operational mode (5 features)
        mode_features = self._encode_operational_mode()
        features.extend(mode_features[:5])

        # 6-10: State transition features (5 features)
        transition_features = self._extract_transition_features()
        features.extend(transition_features[:5])

        return np.array(features[:10], dtype=np.float32)

    def _update_from_message(self, message: CANMessage):
        """Update vehicle state from CAN message"""
        # Map of message IDs to state updates
        if message.id == 0x100:  # Engine data
            self._update_engine_data(message)
        elif message.id == 0x101:  # Brake data
            self._update_brake_data(message)
        elif message.id == 0x102:  # Steering data
            self._update_steering_data(message)
        elif message.id == 0x103:  # Vehicle speed
            self._update_speed_data(message)

    def _update_engine_data(self, message: CANMessage):
        """Update engine-related state"""
        rpm = message.get_signal_value('ENGINE_RPM')
        if rpm is not None:
            self.vehicle_state['rpm'] = rpm

        throttle = message.get_signal_value('THROTTLE_POS')
        if throttle is not None:
            self.vehicle_state['throttle'] = throttle

        temp = message.get_signal_value('ENGINE_TEMP')
        if temp is not None:
            self.vehicle_state['engine_temp'] = temp

    def _update_brake_data(self, message: CANMessage):
        """Update brake-related state"""
        pressure = message.get_signal_value('BRAKE_PRESSURE')
        if pressure is not None:
            self.vehicle_state['brake_pressure'] = pressure

        abs_active = message.get_signal_value('ABS_ACTIVE')
        if abs_active and abs_active > 0:
            self.vehicle_state['systems_active'].add('ABS')
        else:
            self.vehicle_state['systems_active'].discard('ABS')

    def _update_steering_data(self, message: CANMessage):
        """Update steering-related state"""
        angle = message.get_signal_value('STEERING_ANGLE')
        if angle is not None:
            self.vehicle_state['steering_angle'] = angle

    def _update_speed_data(self, message: CANMessage):
        """Update speed-related state"""
        # Average wheel speeds
        speeds = []
        for wheel in ['FL', 'FR', 'RL', 'RR']:
            speed = message.get_signal_value(f'WHEEL_SPEED_{wheel}')
            if speed is not None:
                speeds.append(speed)

        if speeds:
            self.vehicle_state['speed'] = np.mean(speeds)

    def _determine_operational_mode(self) -> VehicleOperationalMode:
        """Determine current operational mode from vehicle state"""
        speed = self.vehicle_state['speed']
        rpm = self.vehicle_state['rpm']
        throttle = self.vehicle_state['throttle']
        brake = self.vehicle_state['brake_pressure']
        steering = abs(self.vehicle_state['steering_angle'])

        # Decision logic
        if rpm == 0:
            return VehicleOperationalMode.IDLE
        elif speed < 5 and rpm > 0:
            if self.vehicle_state['gear'] == 0:
                return VehicleOperationalMode.PARKING
            else:
                return VehicleOperationalMode.STARTING
        elif brake > 20:
            return VehicleOperationalMode.BRAKING
        elif steering > 100:  # Significant steering
            return VehicleOperationalMode.TURNING
        elif throttle > 50 and len(self.state_history) > 1:
            # Check if accelerating
            prev_speed = self.state_history[-2].get('speed', 0)
            if speed > prev_speed + 2:
                return VehicleOperationalMode.ACCELERATING

        # Default to cruising if moving
        if speed > 5:
            return VehicleOperationalMode.CRUISING
        else:
            return VehicleOperationalMode.IDLE

    def _encode_operational_mode(self) -> List[float]:
        """One-hot encode the operational mode"""
        # Map modes to indices (using first 5 modes)
        mode_map = {
            VehicleOperationalMode.IDLE: 0,
            VehicleOperationalMode.STARTING: 1,
            VehicleOperationalMode.ACCELERATING: 2,
            VehicleOperationalMode.CRUISING: 3,
            VehicleOperationalMode.BRAKING: 4
        }

        encoding = [0.0] * 5
        if self.current_mode in mode_map:
            encoding[mode_map[self.current_mode]] = 1.0

        return encoding

    def _extract_transition_features(self) -> List[float]:
        """Extract features related to state transitions"""
        features = []

        # Common transition probabilities
        transitions = [
            (VehicleOperationalMode.IDLE, VehicleOperationalMode.STARTING),
            (VehicleOperationalMode.STARTING, VehicleOperationalMode.ACCELERATING),
            (VehicleOperationalMode.ACCELERATING, VehicleOperationalMode.CRUISING),
            (VehicleOperationalMode.CRUISING, VehicleOperationalMode.BRAKING),
            (VehicleOperationalMode.BRAKING, VehicleOperationalMode.IDLE)
        ]

        for from_mode, to_mode in transitions:
            if from_mode in self.transition_counts:
                total = sum(self.transition_counts[from_mode].values())
                if total > 0:
                    prob = self.transition_counts[from_mode][to_mode] / total
                    features.append(prob)
                else:
                    features.append(0.0)
            else:
                features.append(0.0)

        return features

    def get_context_summary(self) -> Dict[str, any]:
        """Get comprehensive context summary"""
        # Mode distribution
        mode_dist = defaultdict(int)
        for mode in self.mode_history:
            mode_dist[mode.name] += 1

        # Calculate time in each mode
        total_samples = len(self.mode_history)
        mode_percentages = {}
        if total_samples > 0:
            for mode, count in mode_dist.items():
                mode_percentages[mode] = count / total_samples

        return {
            'current_mode': self.current_mode.name,
            'vehicle_state': self.vehicle_state.copy(),
            'environmental_factors': self.environmental_factors.copy(),
            'mode_distribution': dict(mode_dist),
            'mode_percentages': mode_percentages,
            'active_systems': list(self.vehicle_state['systems_active']),
            'transition_matrix': self._get_transition_matrix()
        }

    def _get_transition_matrix(self) -> Dict[str, Dict[str, float]]:
        """Get state transition probability matrix"""
        matrix = {}

        for from_mode, to_modes in self.transition_counts.items():
            total = sum(to_modes.values())
            if total > 0:
                matrix[from_mode.name] = {
                    to_mode.name: count / total
                    for to_mode, count in to_modes.items()
                }

        return matrix

    def get_feature_names(self) -> List[str]:
        """Get names for contextual features"""
        return [
            'mode_idle',
            'mode_starting',
            'mode_accelerating',
            'mode_cruising',
            'mode_braking',
            'trans_idle_to_starting',
            'trans_starting_to_accel',
            'trans_accel_to_cruise',
            'trans_cruise_to_brake',
            'trans_brake_to_idle'
        ]

    def detect_anomalous_context(self) -> Optional[Dict[str, any]]:
        """Detect anomalous contextual conditions"""
        anomalies = []

        # Check for unusual state combinations
        if self.vehicle_state['speed'] > 50 and self.vehicle_state['rpm'] < 500:
            anomalies.append({
                'type': 'speed_rpm_mismatch',
                'description': 'High speed with low RPM',
                'severity': 'high'
            })

        if self.vehicle_state['throttle'] > 80 and self.vehicle_state['brake_pressure'] > 50:
            anomalies.append({
                'type': 'throttle_brake_conflict',
                'description': 'Both throttle and brake engaged',
                'severity': 'critical'
            })

        if self.vehicle_state['engine_temp'] > 120:
            anomalies.append({
                'type': 'engine_overheating',
                'description': 'Engine temperature critical',
                'severity': 'high'
            })

        # Check for impossible transitions
        if len(self.mode_history) >= 2:
            prev_mode = self.mode_history[-2]
            curr_mode = self.mode_history[-1]

            # Define impossible transitions
            impossible = [
                (VehicleOperationalMode.IDLE, VehicleOperationalMode.CRUISING),
                (VehicleOperationalMode.BRAKING, VehicleOperationalMode.ACCELERATING)
            ]

            if (prev_mode, curr_mode) in impossible:
                anomalies.append({
                    'type': 'impossible_transition',
                    'description': f'Invalid transition from {prev_mode.name} to {curr_mode.name}',
                    'severity': 'medium'
                })

        return anomalies if anomalies else None

    def reset(self):
        """Reset the feature extractor"""
        self.state_history.clear()
        self.mode_history.clear()
        self.transition_counts.clear()
        self.current_mode = VehicleOperationalMode.IDLE
        self.vehicle_state = {
            'speed': 0.0,
            'rpm': 0.0,
            'throttle': 0.0,
            'brake_pressure': 0.0,
            'steering_angle': 0.0,
            'gear': 0,
            'engine_temp': 20.0,
            'systems_active': set()
        }