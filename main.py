"""
AMCFF-RL Main Training Script
Orchestrates the complete training pipeline
"""
import os
import sys
import time
import json
import logging
import argparse
import numpy as np
import torch
from datetime import datetime
from typing import Dict, Any, Optional
import wandb  # Optional: for experiment tracking

# Import all components
from config import (
    can_config, feature_config, viz_config, 
    drl_config, sim_config, feedback_config, 
    system_config, CONFIG_GROUPS
)
class Config:
    def __init__(self):
        self.can = can_config
        self.feature = feature_config
        self.visualization = viz_config
        self.drl = drl_config
        self.simulation = sim_config
        self.feedback = feedback_config
        self.system = system_config
        
        # Add convenience properties
        self.data_dir = system_config.data_dir
        self.model_dir = system_config.models_dir
        self.log_dir = system_config.logs_dir
        self.results_dir = system_config.results_dir
        self.cache_dir = system_config.cache_dir

config = Config()
from simulation.environment import CANEnvironment
from feature_extraction.feature_extractor import MultiModalFeatureExtractor
from visualization.visualization_system import VisualizationSystem
from drl_agent.agent import DQNAgent
from feedback.reward_calculator import RewardCalculator

# Setup logging
def setup_logging(log_dir: str):
    """Setup logging configuration"""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

class AMCFFRLTrainer:
    """Main trainer class for AMCFF-RL"""
    
    def __init__(self, args: argparse.Namespace):
        """Initialize trainer with configuration"""
        self.args = args
        self.config = config
        
        # Setup directories
        self.setup_directories()
        
        # Setup logging
        self.logger = setup_logging(self.config.log_dir)
        self.logger.info("Initializing AMCFF-RL Trainer")
        
        # Initialize components
        self.initialize_components()
        
        # Training statistics
        self.episode = 0
        self.total_steps = 0
        self.best_reward = float('-inf')
        
        # Optional: Initialize wandb for experiment tracking
        if args.use_wandb:
            self.init_wandb()
    
    def setup_directories(self):
        """Create necessary directories"""
        dirs = [
            self.config.data_dir,
            self.config.model_dir,
            self.config.log_dir,
            self.config.results_dir,
            os.path.join(self.config.results_dir, 'visualizations'),
            os.path.join(self.config.results_dir, 'vulnerabilities')
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
    
    def initialize_components(self):
        """Initialize all system components"""
        self.logger.info("Initializing components...")
        
        # Environment
        env_config = {
            'timestep': self.config.simulation.simulation_timestep,
            'safety_enabled': True,
            'safety_threshold': 10,
            'dangerous_operations': self.config.fuzzing.dangerous_operations_blacklist
        }
        self.env = CANEnvironment(env_config)
        
        # Feature extractor
        self.feature_extractor = MultiModalFeatureExtractor(self.config.feature)
        
        # Visualization system
        self.viz_system = VisualizationSystem(self.config.visualization)
        
        # Reward calculator
        reward_config = {
            'vulnerability_weight': self.config.reward.vulnerability_weight,
            'exploration_weight': self.config.reward.exploration_weight,
            'coverage_weight': self.config.reward.coverage_weight,
            'novelty_weight': self.config.reward.novelty_weight,
            'anomaly_weights': {
                'timing': self.config.reward.timing_anomaly_weight,
                'value': self.config.reward.value_range_violation_weight,
                'correlation': self.config.reward.correlation_disruption_weight,
                'sequence': self.config.reward.sequence_violation_weight,
                'error': self.config.reward.error_flag_weight
            },
            'safety_violation_penalty': self.config.reward.safety_violation_penalty,
            'repeated_action_penalty': self.config.reward.repeated_action_penalty
        }
        self.reward_calculator = RewardCalculator(reward_config)
        
        # DRL Agent
        agent_config = {
            'state_dim': self.config.feature.total_feature_dim + 15,  # Features + viz metrics
            'action_dim': len(self.config.fuzzing.target_message_ids) * len(self.config.fuzzing.operation_types),
            'feature_dims': {
                'timing': self.config.feature.timing_feature_dim,
                'entropy': self.config.feature.entropy_feature_dim,
                'signal': self.config.feature.signal_feature_dim,
                'contextual': self.config.feature.contextual_feature_dim,
                'frequency': self.config.feature.frequency_feature_dim,
                'visualization': self.config.visualization.visualization_feature_dim
            },
            'learning_rate': self.config.drl.learning_rate,
            'gamma': self.config.drl.gamma,
            'epsilon_start': self.config.drl.epsilon_start,
            'epsilon_end': self.config.drl.epsilon_end,
            'epsilon_decay': self.config.drl.epsilon_decay,
            'buffer_size': self.config.drl.replay_buffer_size,
            'batch_size': self.config.drl.batch_size,
            'target_update_freq': self.config.drl.target_update_freq,
            'priority_alpha': self.config.drl.prioritized_replay_alpha,
            'priority_beta_start': self.config.drl.prioritized_replay_beta_start,
            'device': 'cuda' if torch.cuda.is_available() and self.args.use_cuda else 'cpu'
        }
        self.agent = DQNAgent(agent_config)
        
        self.logger.info("All components initialized successfully")
    
    def init_wandb(self):
        """Initialize Weights & Biases tracking"""
        wandb.init(
            project="amcff-rl",
            name=f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            config={
                'feature_config': self.config.feature.__dict__,
                'drl_config': self.config.drl.__dict__,
                'simulation_config': self.config.simulation.__dict__,
                'fuzzing_config': self.config.fuzzing.__dict__,
                'reward_config': self.config.reward.__dict__
            }
        )
    
    def train(self):
        """Main training loop"""
        self.logger.info("Starting training...")
        
        try:
            while self.episode < self.config.drl.max_episodes:
                self.train_episode()
                
                # Periodic evaluation
                if self.episode % self.args.eval_frequency == 0:
                    self.evaluate()
                
                # Save checkpoint
                if self.episode % self.args.checkpoint_frequency == 0:
                    self.save_checkpoint()
                
                # Update visualizations
                if self.episode % self.args.viz_frequency == 0:
                    self.update_visualizations()
        
        except KeyboardInterrupt:
            self.logger.info("Training interrupted by user")
        
        finally:
            self.cleanup()
    
    def train_episode(self):
        """Train for one episode"""
        episode_start_time = time.time()
        episode_reward = 0
        episode_steps = 0
        
        # Reset environment
        obs = self.env.reset()
        self.feature_extractor.reset()
        
        # Initial state processing
        state = self.process_observation(obs)
        
        done = False
        while not done and episode_steps < self.config.drl.max_steps_per_episode:
            # Select action
            action_idx, action_dict = self.agent.select_action(state)
            
            # Execute action
            next_obs, _, done, info = self.env.step(action_dict)
            
            # Process new observation
            next_state = self.process_observation(next_obs)
            
            # Calculate reward
            reward, reward_components = self.reward_calculator.calculate_reward(
                action_dict,
                obs,
                next_obs,
                info.get('messages_transmitted', []),
                info.get('detected_anomalies', [])
            )
            
            # Store experience
            self.agent.store_experience(state, action_idx, reward, next_state, done)
            
            # Update agent
            loss = self.agent.update()
            
            # Update state
            state = next_state
            obs = next_obs
            
            # Track metrics
            episode_reward += reward
            episode_steps += 1
            self.total_steps += 1
            
            # Log step info
            if self.total_steps % 100 == 0:
                self.log_step_info(reward_components, loss, info)
        
        # Episode complete
        self.episode += 1
        episode_time = time.time() - episode_start_time
        
        # Update agent statistics
        self.agent.episode_rewards.append(episode_reward)
        self.agent.episode_lengths.append(episode_steps)
        
        # Log episode info
        self.log_episode_info(episode_reward, episode_steps, episode_time)
        
        # Track best reward
        if episode_reward > self.best_reward:
            self.best_reward = episode_reward
            self.save_best_model()
    
    def process_observation(self, obs: Dict[str, Any]) -> np.ndarray:
        """Process raw observation into state vector"""
        # Update feature extractors with recent messages
        for msg in obs.get('recent_messages', []):
            self.feature_extractor.update(msg, obs.get('vehicle_state'))
            self.viz_system.update(msg)
        
        # Extract features
        feature_vector = self.feature_extractor.extract()
        
        # Get visualization metrics
        viz_metrics = self.viz_system.get_metrics()
        
        # Combine into state vector
        state = np.concatenate([
            feature_vector.to_array(),
            viz_metrics.to_array()
        ])
        
        return state
    
    def evaluate(self):
        """Evaluate current policy"""
        self.logger.info(f"Evaluating at episode {self.episode}")
        
        eval_rewards = []
        eval_anomalies = []
        
        for _ in range(self.args.eval_episodes):
            episode_reward = 0
            episode_anomalies = 0
            
            obs = self.env.reset()
            state = self.process_observation(obs)
            done = False
            
            while not done:
                # Use greedy policy (no exploration)
                action_idx, action_dict = self.agent.select_action(state, explore=False)
                
                next_obs, _, done, info = self.env.step(action_dict)
                next_state = self.process_observation(next_obs)
                
                reward, _ = self.reward_calculator.calculate_reward(
                    action_dict, obs, next_obs,
                    info.get('messages_transmitted', []),
                    info.get('detected_anomalies', [])
                )
                
                state = next_state
                obs = next_obs
                
                episode_reward += reward
                episode_anomalies += len(info.get('detected_anomalies', []))
            
            eval_rewards.append(episode_reward)
            eval_anomalies.append(episode_anomalies)
        
        # Log evaluation results
        avg_reward = np.mean(eval_rewards)
        avg_anomalies = np.mean(eval_anomalies)
        
        self.logger.info(f"Evaluation - Avg Reward: {avg_reward:.2f}, Avg Anomalies: {avg_anomalies:.2f}")
        
        if self.args.use_wandb:
            wandb.log({
                'eval/avg_reward': avg_reward,
                'eval/avg_anomalies': avg_anomalies,
                'episode': self.episode
            })
    
    def update_visualizations(self):
        """Update and save visualizations"""
        self.logger.info("Updating visualizations...")
        
        viz_dir = os.path.join(self.config.results_dir, 'visualizations', f'episode_{self.episode}')
        self.viz_system.save_visualizations(viz_dir)
    
    def save_checkpoint(self):
        """Save training checkpoint"""
        checkpoint_path = os.path.join(
            self.config.model_dir,
            f'checkpoint_episode_{self.episode}.pth'
        )
        
        self.agent.save(checkpoint_path)
        
        # Save trainer state
        trainer_state = {
            'episode': self.episode,
            'total_steps': self.total_steps,
            'best_reward': self.best_reward,
            'reward_calculator_stats': self.reward_calculator.get_statistics()
        }
        
        state_path = os.path.join(
            self.config.model_dir,
            f'trainer_state_episode_{self.episode}.json'
        )
        
        with open(state_path, 'w') as f:
            json.dump(trainer_state, f, indent=2)
        
        self.logger.info(f"Checkpoint saved at episode {self.episode}")
    
    def save_best_model(self):
        """Save best model so far"""
        best_model_path = os.path.join(self.config.model_dir, 'best_model.pth')
        self.agent.save(best_model_path)
        self.logger.info(f"New best model saved with reward: {self.best_reward:.2f}")
    
    def log_step_info(self, reward_components: Dict[str, float], loss: Optional[float], info: Dict[str, Any]):
        """Log information for a training step"""
        if self.args.use_wandb:
            log_dict = {
                'train/total_reward': reward_components['total'],
                'train/vulnerability_reward': reward_components['vulnerability'],
                'train/exploration_reward': reward_components['exploration'],
                'train/coverage_reward': reward_components['coverage'],
                'train/novelty_reward': reward_components['novelty'],
                'train/safety_penalty': reward_components['safety_penalty'],
                'train/repetition_penalty': reward_components['repetition_penalty'],
                'train/anomalies_detected': len(info.get('detected_anomalies', [])),
                'train/messages_transmitted': info.get('messages_transmitted', 0),
                'train/epsilon': self.agent.epsilon,
                'train/buffer_size': len(self.agent.memory),
                'step': self.total_steps
            }
            
            if loss is not None:
                log_dict['train/loss'] = loss
            
            wandb.log(log_dict)
    
    def log_episode_info(self, episode_reward: float, episode_steps: int, episode_time: float):
        """Log episode information"""
        agent_stats = self.agent.get_statistics()
        reward_stats = self.reward_calculator.get_statistics()
        
        self.logger.info(
            f"Episode {self.episode} - "
            f"Reward: {episode_reward:.2f}, "
            f"Steps: {episode_steps}, "
            f"Time: {episode_time:.2f}s, "
            f"Epsilon: {agent_stats['epsilon']:.4f}, "
            f"Anomalies: {reward_stats['total_anomalies']}"
        )
        
        if self.args.use_wandb:
            wandb.log({
                'episode/reward': episode_reward,
                'episode/steps': episode_steps,
                'episode/time': episode_time,
                'episode/explored_states': reward_stats['explored_states'],
                'episode/message_coverage': reward_stats['message_coverage'],
                'episode/unique_actions': reward_stats['unique_actions'],
                'episode': self.episode
            })
    
    def cleanup(self):
        """Clean up resources"""
        self.logger.info("Cleaning up...")
        
        # Save final checkpoint
        self.save_checkpoint()
        
        # Generate final report
        self.generate_final_report()
        
        if self.args.use_wandb:
            wandb.finish()
    
    def generate_final_report(self):
        """Generate final training report"""
        report = {
            'training_summary': {
                'total_episodes': self.episode,
                'total_steps': self.total_steps,
                'best_reward': self.best_reward,
                'final_epsilon': self.agent.epsilon
            },
            'agent_statistics': self.agent.get_statistics(),
            'reward_statistics': self.reward_calculator.get_statistics(),
            'discovered_vulnerabilities': self._summarize_vulnerabilities()
        }
        
        report_path = os.path.join(self.config.results_dir, 'final_report.json')
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Final report saved to {report_path}")
    
    def _summarize_vulnerabilities(self) -> Dict[str, Any]:
        """Summarize discovered vulnerabilities"""
        # Analyze anomaly history
        anomaly_types = {}
        for anomaly in self.reward_calculator.anomaly_history:
            anomaly_type = anomaly.get('type', 'unknown')
            anomaly_types[anomaly_type] = anomaly_types.get(anomaly_type, 0) + 1
        
        return {
            'total_anomalies': len(self.reward_calculator.anomaly_history),
            'anomaly_types': anomaly_types,
            'unique_anomaly_types': len(anomaly_types)
        }

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='AMCFF-RL Training Script')
    
    # Training arguments
    parser.add_argument('--eval-frequency', type=int, default=10,
                       help='Evaluate every N episodes')
    parser.add_argument('--checkpoint-frequency', type=int, default=50,
                       help='Save checkpoint every N episodes')
    parser.add_argument('--viz-frequency', type=int, default=25,
                       help='Update visualizations every N episodes')
    parser.add_argument('--eval-episodes', type=int, default=5,
                       help='Number of episodes for evaluation')
    
    # System arguments
    parser.add_argument('--use-cuda', action='store_true',
                       help='Use CUDA if available')
    parser.add_argument('--use-wandb', action='store_true',
                       help='Use Weights & Biases for tracking')
    parser.add_argument('--resume', type=str, default=None,
                       help='Resume from checkpoint')
    
    return parser.parse_args()

def main():
    """Main entry point"""
    # Parse arguments
    args = parse_arguments()
    
    # Create trainer
    trainer = AMCFFRLTrainer(args)
    
    # Start training
    trainer.train()

if __name__ == '__main__':
    main()